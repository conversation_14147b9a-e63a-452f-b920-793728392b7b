#!/usr/bin/env bash

# Simple VSCodium Runner with Dynamic Fingerprint Generation
# Runs VSCodium with different fingerprints on each execution

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
FINGERPRINTS_DIR="$PROJECT_ROOT/.fingerprints"
PROFILES_DIR="$PROJECT_ROOT/.vscodium-profiles"
VSCODIUM_CMD=""

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_step() { echo -e "${PURPLE}🔧 $1${NC}"; }

show_help() {
    cat << EOF
Sandboxed VSCodium Fingerprint Runner

USAGE:
    $0 [OPTIONS] [WORKSPACE_PATH]

OPTIONS:
    --new-fingerprint, -n    Generate new fingerprint and run Sandboxed VSCodium
    --fingerprint-id ID      Use specific fingerprint by ID
    --privacy-level LEVEL    Privacy level (low, medium, high, maximum) [default: high]
    --sandbox-level LEVEL    Sandbox level (none, basic, strict, maximum) [default: strict]
    --sandbox-type TYPE      Sandbox type (auto, docker, namespace, macos, firejail) [default: auto]
    --network-isolation NET  Network isolation (allow, restrict, block) [default: restrict]
    --resource-limits        Enable CPU/memory limits [default: enabled]
    --list, -l               List available fingerprints
    --clean, -c              Clean up old profiles and fingerprints
    --help, -h               Show this help

EXAMPLES:
    $0 --new-fingerprint                           # New fingerprint, strict sandbox
    $0 --new-fingerprint --sandbox-level maximum   # Maximum security sandbox
    $0 --fingerprint-id fp_20241201_123456         # Use existing fingerprint
    $0 --sandbox-level none                        # Disable sandboxing
    $0 --sandbox-type docker --network-isolation block  # Docker with no network

SANDBOX LEVELS:
    none     - No sandboxing (profile isolation only)
    basic    - Process isolation + restricted file access
    strict   - + Network restrictions + resource limits
    maximum  - + Full system isolation + minimal permissions

SANDBOX TYPES:
    auto      - Automatically select best available sandbox
    docker    - Container-based isolation (strongest)
    namespace - Linux namespace isolation
    macos     - macOS App Sandbox
    firejail  - Firejail-based sandboxing
    basic     - Enhanced profile isolation

ISOLATION:
    - Profiles are stored locally in: $PROFILES_DIR
    - Fingerprints are stored in: $FINGERPRINTS_DIR
    - Each Sandboxed VSCodium instance runs in complete isolation
    - No interference with your main VSCodium installation
    - Configurable security levels for different use cases

VSCODIUM INSTALLATION:
    macOS (Homebrew): brew install --cask vscodium
    macOS (Manual):   Download from https://vscodium.com/
    Linux (Snap):     snap install codium --classic
    Linux (Flatpak):  flatpak install com.vscodium.codium

SANDBOX DEPENDENCIES:
    Docker:    docker or podman
    Namespace: unshare, mount (util-linux)
    macOS:     sandbox-exec (built-in)
    Firejail:  firejail package

EOF
}

# Parse arguments
GENERATE_NEW=false
FINGERPRINT_ID=""
PRIVACY_LEVEL="high"
SANDBOX_LEVEL="strict"
SANDBOX_TYPE="auto"
NETWORK_ISOLATION="restrict"
RESOURCE_LIMITS=true
LIST_MODE=false
CLEAN_MODE=false
WORKSPACE_PATH="$(pwd)"

while [[ $# -gt 0 ]]; do
    case $1 in
        --new-fingerprint|-n)
            GENERATE_NEW=true
            shift
            ;;
        --fingerprint-id)
            FINGERPRINT_ID="$2"
            shift 2
            ;;
        --privacy-level)
            PRIVACY_LEVEL="$2"
            shift 2
            ;;
        --sandbox-level)
            SANDBOX_LEVEL="$2"
            shift 2
            ;;
        --sandbox-type)
            SANDBOX_TYPE="$2"
            shift 2
            ;;
        --network-isolation)
            NETWORK_ISOLATION="$2"
            shift 2
            ;;
        --resource-limits)
            RESOURCE_LIMITS=true
            shift
            ;;
        --no-resource-limits)
            RESOURCE_LIMITS=false
            shift
            ;;
        --list|-l)
            LIST_MODE=true
            shift
            ;;
        --clean|-c)
            CLEAN_MODE=true
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        -*)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
        *)
            WORKSPACE_PATH="$1"
            shift
            ;;
    esac
done

# Find the correct VSCodium binary
find_vscodium() {
    local vscodium_cmd=""

    # Check common VSCodium locations on macOS
    if [[ -f "/Applications/VSCodium.app/Contents/Resources/app/bin/codium" ]]; then
        vscodium_cmd="/Applications/VSCodium.app/Contents/Resources/app/bin/codium"
    elif [[ -f "/usr/local/bin/codium" ]]; then
        vscodium_cmd="/usr/local/bin/codium"
    elif command -v codium &> /dev/null; then
        vscodium_cmd="codium"
    elif [[ -f "/opt/homebrew/bin/codium" ]]; then
        # Homebrew on Apple Silicon
        vscodium_cmd="/opt/homebrew/bin/codium"
    elif command -v vscodium &> /dev/null; then
        # Alternative command name on some systems
        vscodium_cmd="vscodium"
    elif [[ -f "/snap/bin/codium" ]]; then
        # Snap installation
        vscodium_cmd="/snap/bin/codium"
    elif command -v flatpak &> /dev/null && flatpak list | grep -q "com.vscodium.codium"; then
        # Flatpak installation
        vscodium_cmd="flatpak run com.vscodium.codium"
    fi

    if [[ -z "$vscodium_cmd" ]]; then
        log_error "VSCodium not found!" >&2
        log_info "Please install VSCodium to run isolated instances:" >&2
        log_info "  macOS (Homebrew): brew install --cask vscodium" >&2
        log_info "  macOS (Manual):   Download from https://vscodium.com/" >&2
        log_info "  Linux (Snap):     snap install codium --classic" >&2
        log_info "  Linux (Flatpak):  flatpak install com.vscodium.codium" >&2
        exit 1
    fi

    log_info "Found VSCodium for isolation: $vscodium_cmd" >&2
    echo "$vscodium_cmd"
}

# Check if VSCodium is available
check_vscodium() {
    VSCODIUM_CMD=$(find_vscodium 2>&1 | tail -1)
    if [[ -z "$VSCODIUM_CMD" ]]; then
        log_error "Failed to find VSCodium executable" >&2
        exit 1
    fi
}

# Detect available sandbox capabilities
detect_sandbox_capabilities() {
    local capabilities=()

    # Check for Docker
    if command -v docker &> /dev/null && docker info &> /dev/null; then
        capabilities+=("docker")
    elif command -v podman &> /dev/null && podman info &> /dev/null; then
        capabilities+=("podman")
    fi

    # Check for Linux namespaces
    if [[ "$OSTYPE" == "linux-gnu"* ]] && command -v unshare &> /dev/null; then
        capabilities+=("namespace")
    fi

    # Check for macOS sandbox
    if [[ "$OSTYPE" == "darwin"* ]] && command -v sandbox-exec &> /dev/null; then
        capabilities+=("macos")
    fi

    # Check for Firejail
    if command -v firejail &> /dev/null; then
        capabilities+=("firejail")
    fi

    # Basic isolation is always available
    capabilities+=("basic")

    printf "%s\n" "${capabilities[@]}"
}

# Select best sandbox type
select_sandbox_type() {
    local requested="$1"
    local capabilities
    # Use read loop instead of mapfile for macOS compatibility
    capabilities=()
    while IFS= read -r line; do
        capabilities+=("$line")
    done < <(detect_sandbox_capabilities)

    if [[ "$requested" != "auto" ]]; then
        # Check if requested type is available
        for cap in "${capabilities[@]}"; do
            if [[ "$cap" == "$requested" ]] || [[ "$requested" == "docker" && "$cap" == "podman" ]]; then
                echo "$requested"
                return 0
            fi
        done
        log_warning "Requested sandbox type '$requested' not available, falling back to auto-selection"
    fi

    # Auto-select best available sandbox
    for preferred in "docker" "podman" "namespace" "macos" "firejail" "basic"; do
        for cap in "${capabilities[@]}"; do
            if [[ "$cap" == "$preferred" ]]; then
                echo "$preferred"
                return 0
            fi
        done
    done

    echo "basic"
}

# Validate sandbox configuration
validate_sandbox_config() {
    case "$SANDBOX_LEVEL" in
        none|basic|strict|maximum) ;;
        *)
            log_error "Invalid sandbox level: $SANDBOX_LEVEL"
            log_info "Valid levels: none, basic, strict, maximum"
            exit 1
            ;;
    esac

    case "$NETWORK_ISOLATION" in
        allow|restrict|block) ;;
        *)
            log_error "Invalid network isolation: $NETWORK_ISOLATION"
            log_info "Valid options: allow, restrict, block"
            exit 1
            ;;
    esac
}

# Comprehensive sandbox health check
check_sandbox_health() {
    local sandbox_type="$1"
    local profile_dir="$2"

    log_step "Performing sandbox health check..."

    local health_status="healthy"
    local issues=()

    # Check sandbox-specific requirements
    case "$sandbox_type" in
        docker|podman)
            if ! command -v docker &> /dev/null && ! command -v podman &> /dev/null; then
                issues+=("Container runtime not available")
                health_status="unhealthy"
            elif command -v docker &> /dev/null && ! docker info &> /dev/null; then
                issues+=("Docker daemon not running")
                health_status="unhealthy"
            elif command -v podman &> /dev/null && ! podman info &> /dev/null; then
                issues+=("Podman not properly configured")
                health_status="unhealthy"
            fi
            ;;
        namespace)
            if ! command -v unshare &> /dev/null; then
                issues+=("unshare command not available")
                health_status="unhealthy"
            elif [[ $EUID -ne 0 ]] && ! unshare --user --map-root-user true &> /dev/null; then
                issues+=("User namespaces not available")
                health_status="degraded"
            fi
            ;;
        macos)
            if [[ "$OSTYPE" != "darwin"* ]]; then
                issues+=("macOS sandbox not available on this platform")
                health_status="unhealthy"
            elif ! command -v sandbox-exec &> /dev/null; then
                issues+=("sandbox-exec not available")
                health_status="unhealthy"
            fi
            ;;
        firejail)
            if ! command -v firejail &> /dev/null; then
                issues+=("Firejail not installed")
                health_status="unhealthy"
            fi
            ;;
    esac

    # Check filesystem permissions
    if [[ ! -w "$profile_dir" ]]; then
        issues+=("Profile directory not writable: $profile_dir")
        health_status="unhealthy"
    fi

    # Check available disk space (require at least 1GB)
    local available_space
    available_space=$(df "$profile_dir" | awk 'NR==2 {print $4}')
    if [[ "$available_space" -lt 1048576 ]]; then  # 1GB in KB
        issues+=("Insufficient disk space (less than 1GB available)")
        health_status="degraded"
    fi

    # Check memory availability (require at least 2GB)
    local available_memory
    if command -v free &> /dev/null; then
        available_memory=$(free -k | awk '/^Mem:/ {print $7}')
        if [[ "$available_memory" -lt 2097152 ]]; then  # 2GB in KB
            issues+=("Insufficient available memory (less than 2GB)")
            health_status="degraded"
        fi
    fi

    # Report health status
    case "$health_status" in
        healthy)
            log_success "Sandbox health check passed"
            ;;
        degraded)
            log_warning "Sandbox health check shows degraded performance:"
            for issue in "${issues[@]}"; do
                log_warning "  - $issue"
            done
            ;;
        unhealthy)
            log_error "Sandbox health check failed:"
            for issue in "${issues[@]}"; do
                log_error "  - $issue"
            done
            return 1
            ;;
    esac

    return 0
}

# Monitor sandbox resource usage
monitor_sandbox_resources() {
    local profile_name="$1"
    local monitor_duration="${2:-300}"  # Default 5 minutes

    log_info "Starting sandbox resource monitoring for $monitor_duration seconds..."

    local monitor_log="$PROFILES_DIR/sandbox-monitor-$(date +%Y%m%d_%H%M%S).log"

    {
        echo "Sandbox Resource Monitor - Started at $(date)"
        echo "Profile: $profile_name"
        echo "Duration: $monitor_duration seconds"
        echo "----------------------------------------"
    } > "$monitor_log"

    local start_time=$(date +%s)
    local end_time=$((start_time + monitor_duration))

    while [[ $(date +%s) -lt $end_time ]]; do
        local current_time=$(date '+%Y-%m-%d %H:%M:%S')

        # Find processes related to this profile
        local pids
        # Use read loop instead of mapfile for macOS compatibility
        pids=()
        while IFS= read -r line; do
            [[ -n "$line" ]] && pids+=("$line")
        done < <(pgrep -f "$profile_name" 2>/dev/null || true)

        if [[ ${#pids[@]} -gt 0 ]]; then
            {
                echo "[$current_time] Active processes: ${#pids[@]}"

                # Get detailed process information
                for pid in "${pids[@]}"; do
                    if ps -p "$pid" > /dev/null 2>&1; then
                        local proc_info
                        proc_info=$(ps -p "$pid" -o pid,ppid,pcpu,pmem,vsz,rss,comm --no-headers 2>/dev/null || echo "Process $pid no longer exists")
                        echo "  PID $pid: $proc_info"
                    fi
                done

                # Check system resources
                if command -v free &> /dev/null; then
                    local mem_info
                    mem_info=$(free -h | grep '^Mem:' | awk '{print "Used: " $3 "/" $2 " (" $3/$2*100 "%)"}')
                    echo "  Memory: $mem_info"
                fi

                if command -v df &> /dev/null; then
                    local disk_info
                    disk_info=$(df -h "$PROFILES_DIR" | awk 'NR==2 {print "Used: " $3 "/" $2 " (" $5 ")"}')
                    echo "  Disk: $disk_info"
                fi

                echo ""
            } >> "$monitor_log"
        else
            echo "[$current_time] No active processes found" >> "$monitor_log"
        fi

        sleep 30
    done

    {
        echo "----------------------------------------"
        echo "Monitoring completed at $(date)"
    } >> "$monitor_log"

    log_info "Resource monitoring completed. Log saved to: $monitor_log"
}

# Sandbox security audit
audit_sandbox_security() {
    local sandbox_type="$1"
    local profile_dir="$2"

    log_step "Performing sandbox security audit..."
    log_info "Audit parameters: sandbox_type=$sandbox_type, profile_dir=$profile_dir"

    # Ensure profile directory exists
    if [[ ! -d "$profile_dir" ]]; then
        log_warning "Profile directory does not exist yet: $profile_dir"
        log_warning "Skipping security audit - will be performed after profile creation"
        return 0
    fi

    local audit_log="$profile_dir/security-audit-$(date +%Y%m%d_%H%M%S).log"
    log_info "Creating audit log: $audit_log"

    {
        echo "Sandbox Security Audit Report"
        echo "Generated: $(date)"
        echo "Sandbox Type: $sandbox_type"
        echo "Security Level: $SANDBOX_LEVEL"
        echo "Network Isolation: $NETWORK_ISOLATION"
        echo "Resource Limits: $RESOURCE_LIMITS"
        echo "========================================"
        echo ""
    } > "$audit_log"

    # Check file permissions (with timeout protection)
    {
        echo "File Permissions Audit:"
        echo "-----------------------"
        if [[ -d "$profile_dir" ]]; then
            timeout 10 find "$profile_dir" -type f -perm /022 -ls 2>/dev/null | head -20 || echo "File permission check timeout or unavailable"
        else
            echo "Profile directory not yet created: $profile_dir"
        fi
        echo ""
    } >> "$audit_log"

    # Check for sensitive files (with timeout protection)
    {
        echo "Sensitive Files Check:"
        echo "----------------------"
        if [[ -d "$profile_dir" ]]; then
            timeout 10 find "$profile_dir" -name "*.key" -o -name "*.pem" -o -name "*password*" -o -name "*secret*" 2>/dev/null | head -10 || echo "Sensitive files check timeout or unavailable"
        else
            echo "Profile directory not yet created: $profile_dir"
        fi
        echo ""
    } >> "$audit_log"

    # Check network configuration (with timeout protection)
    {
        echo "Network Configuration:"
        echo "----------------------"
        echo "Network Isolation: $NETWORK_ISOLATION"
        if command -v netstat &> /dev/null; then
            echo "Active network connections:"
            timeout 5 netstat -tuln 2>/dev/null | head -10 || echo "Network status timeout or unavailable"
        else
            echo "netstat command not available"
        fi
        echo ""
    } >> "$audit_log"

    # Check process isolation
    {
        echo "Process Isolation Check:"
        echo "------------------------"
        echo "Current user: $(whoami 2>/dev/null || echo 'N/A')"
        echo "Current UID/GID: $(id 2>/dev/null || echo 'N/A')"
        echo "Process limits:"
        ulimit -a 2>/dev/null | head -10 || echo "Process limits unavailable"
        echo ""
    } >> "$audit_log"

    # Sandbox-specific checks
    case "$sandbox_type" in
        docker|podman)
            {
                echo "Container Security:"
                echo "-------------------"
                if command -v docker &> /dev/null; then
                    echo "Docker version: $(docker --version 2>/dev/null || echo 'N/A')"
                    echo "Docker info:"
                    timeout 10 docker info 2>/dev/null | grep -E "(Security|Runtime|Storage)" | head -5 || echo "Docker info timeout or unavailable"
                fi
                echo ""
            } >> "$audit_log"
            ;;
        namespace)
            {
                echo "Namespace Security:"
                echo "-------------------"
                echo "Available namespaces:"
                ls -la /proc/self/ns/ 2>/dev/null | head -10 || echo "Namespace info unavailable"
                echo ""
            } >> "$audit_log"
            ;;
        macos)
            {
                echo "macOS Sandbox Security:"
                echo "-----------------------"
                if command -v sandbox-exec &> /dev/null; then
                    echo "sandbox-exec available: Yes"
                    echo "macOS version: $(sw_vers -productVersion 2>/dev/null || echo 'N/A')"
                    echo "System Integrity Protection: $(csrutil status 2>/dev/null || echo 'N/A')"
                else
                    echo "sandbox-exec available: No"
                fi
                echo "Current sandbox profile: ${profile_dir}/enhanced-sandbox.sb"
                if [[ -f "${profile_dir}/enhanced-sandbox.sb" ]]; then
                    echo "Sandbox profile size: $(wc -l < "${profile_dir}/enhanced-sandbox.sb" 2>/dev/null || echo 'N/A') lines"
                else
                    echo "Sandbox profile: Not yet created"
                fi
                echo ""
            } >> "$audit_log"
            ;;
        firejail)
            {
                echo "Firejail Security:"
                echo "------------------"
                if command -v firejail &> /dev/null; then
                    echo "Firejail version: $(firejail --version 2>/dev/null | head -1 || echo 'N/A')"
                    echo "Active Firejail processes:"
                    timeout 5 firejail --list 2>/dev/null | head -5 || echo "Firejail list timeout or unavailable"
                fi
                echo ""
            } >> "$audit_log"
            ;;
        basic|*)
            {
                echo "Basic Sandbox Security:"
                echo "-----------------------"
                echo "Sandbox type: $sandbox_type"
                echo "Process isolation: Profile-based"
                echo "Resource limits: $RESOURCE_LIMITS"
                echo "Enhanced wrapper: ${profile_dir}/user-data/enhanced-sandboxed-launcher.sh"
                echo ""
            } >> "$audit_log"
            ;;
    esac

    log_success "Security audit completed. Report saved to: $audit_log"
}

# List fingerprints
list_fingerprints() {
    log_step "Available fingerprints:"
    
    if [[ ! -d "$FINGERPRINTS_DIR" ]]; then
        log_warning "No fingerprints found. Generate one with --new-fingerprint"
        return
    fi
    
    local count=0
    for fp_file in "$FINGERPRINTS_DIR"/*.json; do
        if [[ -f "$fp_file" ]]; then
            local fp_id=$(basename "$fp_file" .json)
            local created=$(stat -f %Sm "$fp_file" 2>/dev/null || stat -c %y "$fp_file" 2>/dev/null || echo "unknown")
            echo "  📋 $fp_id (created: $created)"
            ((count++))
        fi
    done
    
    if [[ $count -eq 0 ]]; then
        log_warning "No fingerprints found"
    else
        log_success "Found $count fingerprint(s)"
    fi
}

# Clean old data
cleanup() {
    log_step "Cleaning up old data..."

    # Clean old profiles (older than 7 days) from project-local directory
    if [[ -d "$PROFILES_DIR" ]]; then
        log_info "Cleaning profiles older than 7 days from: $PROFILES_DIR"
        find "$PROFILES_DIR" -type d -name "vscodium-fp-*" -mtime +7 -exec rm -rf {} + 2>/dev/null || true
    fi

    # Also clean any old profiles from the legacy /tmp location
    if [[ -d "/tmp/vscodium-fingerprint-profiles" ]]; then
        log_info "Cleaning legacy profiles from: /tmp/vscodium-fingerprint-profiles"
        find "/tmp/vscodium-fingerprint-profiles" -type d -name "vscodium-fp-*" -mtime +7 -exec rm -rf {} + 2>/dev/null || true
    fi

    # Clean old fingerprints (older than 30 days)
    if [[ -d "$FINGERPRINTS_DIR" ]]; then
        log_info "Cleaning fingerprints older than 30 days from: $FINGERPRINTS_DIR"
        find "$FINGERPRINTS_DIR" -name "*.json" -mtime +30 -delete 2>/dev/null || true
    fi

    log_success "Cleanup completed"
}

# Generate simple mock fingerprint (no Rust dependency)
generate_mock_fingerprint() {
    local fp_id="$1"
    local fp_path="$2"

    log_step "Generating mock fingerprint: $fp_id" >&2
    
    mkdir -p "$(dirname "$fp_path")"
    
    # Generate a simple JSON fingerprint with VSCodium-specific metadata
    cat > "$fp_path" << EOF
{
  "id": "$fp_id",
  "version": "1.0.0",
  "created_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "privacy_level": "$PRIVACY_LEVEL",
  "rotation_enabled": true,
  "editor_type": "vscodium",
  "device_id": "$(openssl rand -hex 16)",
  "hardware_signature": {
    "cpu_signature": "mock-cpu-$(openssl rand -hex 4)",
    "memory_class": "8-16GB",
    "architecture": "$(uname -m)",
    "hardware_uuid_hash": "$(openssl rand -hex 16)",
    "performance_class": "high"
  },
  "system_signature": {
    "os_family": "$(uname -s)",
    "os_version_class": "recent",
    "timezone_class": "$(date +%Z)",
    "locale_class": "en-US"
  },
  "vscodium_signature": {
    "version_class": "1.80-1.90",
    "machine_id_hash": "$(openssl rand -hex 16)",
    "session_id_hash": "$(openssl rand -hex 16)",
    "telemetry_disabled": true,
    "marketplace_override": "open-vsx.org"
  },
  "network_signature": {
    "mac_signature": "$(openssl rand -hex 12)",
    "network_class": "ethernet"
  },
  "privacy_metadata": {
    "anonymization_level": "$PRIVACY_LEVEL",
    "data_minimization_applied": true,
    "tracking_protection_enabled": true,
    "microsoft_telemetry_disabled": true
  },
  "fingerprint_hash": "$(openssl rand -hex 32)"
}
EOF
    
    log_success "Mock fingerprint generated: $fp_id" >&2
}

# Try to use Rust tool, fallback to mock
generate_fingerprint() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local fp_id="fp_${timestamp}"
    local fp_path="$FINGERPRINTS_DIR/${fp_id}.json"

    # Try Rust tool first
    if [[ -f "$PROJECT_ROOT/Cargo.toml" ]] && command -v cargo &> /dev/null; then
        log_step "Attempting to use Rust fingerprint generator..." >&2

        cd "$PROJECT_ROOT"
        # Set environment variable to bypass ethics prompt for automated usage
        export SECURITY_TOOLS_ETHICS_ACCEPTED=true
        # Redirect all output to suppress colored logs, only check exit code
        if cargo run --release -p privacy-fingerprint-generator -- \
            --privacy-level "$PRIVACY_LEVEL" \
            --enable-rotation \
            --format json \
            generate \
            --real-system \
            --save-path "$fp_path" >/dev/null 2>&1; then
            log_success "Rust fingerprint generated: $fp_id" >&2
            # Output only the fingerprint ID to stdout (no extra formatting)
            printf "%s" "$fp_id"
            return
        else
            log_warning "Rust tool failed, using mock fingerprint" >&2
        fi
    fi

    # Fallback to mock
    generate_mock_fingerprint "$fp_id" "$fp_path" >&2
    # Output only the fingerprint ID to stdout (no extra formatting)
    printf "%s" "$fp_id"
}

# Create isolated VSCodium profile
create_profile() {
    local fp_id="$1"
    local fp_path="$FINGERPRINTS_DIR/${fp_id}.json"

    local profile_name="vscodium-fp-${fp_id}"
    local profile_dir="$PROFILES_DIR/$profile_name"
    local user_data_dir="$profile_dir/user-data"
    local extensions_dir="$profile_dir/extensions"

    log_step "Creating isolated profile for VSCodium instance: $profile_name" >&2
    log_info "Isolated profile directory: $profile_dir" >&2

    # Create directories
    if ! mkdir -p "$user_data_dir/User/globalStorage"; then
        log_error "Failed to create user data directory: $user_data_dir/User/globalStorage" >&2
        exit 1
    fi

    if ! mkdir -p "$extensions_dir"; then
        log_error "Failed to create extensions directory: $extensions_dir" >&2
        exit 1
    fi

    # Copy fingerprint
    if [[ -f "$fp_path" ]]; then
        if ! cp "$fp_path" "$user_data_dir/User/globalStorage/fingerprint.json"; then
            log_error "Failed to copy fingerprint to profile" >&2
            exit 1
        fi
        log_info "Fingerprint installed: $user_data_dir/User/globalStorage/fingerprint.json" >&2
    else
        log_warning "Fingerprint file not found: $fp_path" >&2
    fi
    
    # Create VSCodium settings with privacy-focused configuration and single window isolation
    cat > "$user_data_dir/User/settings.json" << EOF
{
  "security.workspace.trust.enabled": false,
  "telemetry.telemetryLevel": "off",
  "update.mode": "none",
  "extensions.autoUpdate": false,
  "workbench.enableExperiments": false,
  "workbench.startupEditor": "none",
  "window.restoreWindows": "none",
  "window.newWindowDimensions": "inherit",
  "window.openFilesInNewWindow": "off",
  "window.openFoldersInNewWindow": "off",
  "window.openWithoutArgumentsInNewWindow": "off",
  "window.restoreFullscreen": false,
  "window.titleBarStyle": "custom",
  "window.menuBarVisibility": "toggle",
  "window.title": "🔒 Isolated VSCodium ($fp_id) - \${activeEditorShort}\${separator}\${rootName}",
  "window.titleSeparator": " - ",
  "window.customTitleBarVisibility": "auto",
  "workbench.editor.enablePreview": false,
  "workbench.editor.enablePreviewFromQuickOpen": false,
  "workbench.editor.showTabs": true,
  "workbench.editor.tabCloseButton": "right",
  "workbench.editor.tabSizing": "fit",
  "workbench.editor.wrapTabs": false,
  "workbench.editor.scrollToSwitchTabs": false,
  "workbench.editor.focusRecentEditorAfterClose": true,
  "workbench.editor.limit.enabled": false,
  "workbench.editor.restoreViewState": false,
  "workbench.colorTheme": "Default Dark+",
  "workbench.iconTheme": "vs-seti",
  "workbench.productIconTheme": "Default",
  "privacy.fingerprintIsolation": true,
  "privacy.profileName": "$profile_name",
  "privacy.fingerprintId": "$fp_id",
  "privacy.fingerprintPath": "$user_data_dir/User/globalStorage/fingerprint.json",
  "privacy.privacyLevel": "$PRIVACY_LEVEL",
  "augment.fingerprintOverride": "$user_data_dir/User/globalStorage/fingerprint.json",
  "extensions.gallery.serviceUrl": "https://open-vsx.org/vscode/gallery",
  "extensions.gallery.itemUrl": "https://open-vsx.org/vscode/item",
  "extensions.gallery.searchUrl": "https://open-vsx.org/vscode/search",
  "extensions.gallery.resourceUrlTemplate": "https://open-vsx.org/vscode/unpkg/{publisher}/{name}/{version}/{path}",
  "extensions.gallery.cacheUrl": "https://open-vsx.org/vscode/cache",
  "extensions.gallery.controlUrl": "https://open-vsx.org/vscode/control",
  "extensions.gallery.nlsBaseUrl": "https://www.bing.com/api/v7.0/suggestions",
  "editor.suggestSelection": "first",
  "vsintellicode.modify.editor.suggestSelection": "automaticallyOverrodeDefaultValue"
}
EOF
    
    # Create product.json override for Isolated VSCodium to use Open VSX Registry
    cat > "$user_data_dir/product.json" << EOF
{
  "nameShort": "🔒 Isolated VSCodium",
  "nameLong": "🔒 Isolated VSCodium (Fingerprint: $fp_id)",
  "applicationName": "isolated-vscodium",
  "dataFolderName": ".isolated-vscodium",
  "win32MutexName": "isolated-vscodium-$fp_id",
  "licenseName": "MIT",
  "licenseUrl": "https://github.com/VSCodium/vscodium/blob/master/LICENSE",
  "serverDataFolderName": ".isolated-vscodium-server",
  "serverApplicationName": "isolated-vscodium-server",
  "extensionsGallery": {
    "serviceUrl": "https://open-vsx.org/vscode/gallery",
    "itemUrl": "https://open-vsx.org/vscode/item",
    "searchUrl": "https://open-vsx.org/vscode/search",
    "resourceUrlTemplate": "https://open-vsx.org/vscode/unpkg/{publisher}/{name}/{version}/{path}",
    "cacheUrl": "https://open-vsx.org/vscode/cache",
    "controlUrl": "https://open-vsx.org/vscode/control",
    "nlsBaseUrl": "https://www.bing.com/api/v7.0/suggestions"
  },
  "linkProtectionTrustedDomains": [
    "https://open-vsx.org",
    "https://vscodium.com"
  ],
  "documentationUrl": "https://go.microsoft.com/fwlink/?LinkID=533484#vscodium",
  "requestFeatureUrl": "https://github.com/VSCodium/vscodium/issues",
  "reportIssueUrl": "https://github.com/VSCodium/vscodium/issues",
  "tipsAndTricksUrl": "https://go.microsoft.com/fwlink/?linkid=852118",
  "twitterUrl": "https://twitter.com/code",
  "privacyStatementUrl": "https://vscodium.com/",
  "telemetryOptOutUrl": "https://vscodium.com/"
}
EOF
    
    log_success "Profile created: $profile_dir" >&2
    # Output only the profile directory to stdout (no extra formatting)
    printf "%s" "$profile_dir"
}

# Check for existing Isolated VSCodium instances using this profile
check_existing_instance() {
    local user_data_dir="$1"
    local profile_name="$2"

    # Check if Isolated VSCodium is already running with this profile
    if pgrep -f "$user_data_dir" > /dev/null 2>&1; then
        log_warning "Isolated VSCodium instance already running with profile: $profile_name"
        log_info "Bringing existing window to front instead of creating new one..."

        # Try to bring the existing window to front (macOS specific)
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # Try to activate the isolated instance
            osascript -e 'tell application "IsolatedVSCodium" to activate' 2>/dev/null || \
            osascript -e 'tell application "VSCodium" to activate' 2>/dev/null || true
        fi

        return 1  # Instance already exists
    fi

    return 0  # No existing instance
}

# Enhanced Docker sandbox implementation with comprehensive security
launch_with_docker_sandbox() {
    local profile_dir="$1"
    local user_data_dir="$profile_dir/user-data"
    local extensions_dir="$profile_dir/extensions"
    local profile_name=$(basename "$profile_dir")

    log_step "Launching VSCodium in enhanced Docker sandbox..."

    # Determine container runtime
    local container_cmd="docker"
    if command -v podman &> /dev/null && ! command -v docker &> /dev/null; then
        container_cmd="podman"
        log_info "Using Podman as container runtime"
    else
        log_info "Using Docker as container runtime"
    fi

    # Validate container runtime is working
    if ! $container_cmd info &> /dev/null; then
        log_error "Container runtime '$container_cmd' is not available or not running"
        return 1
    fi

    # Create enhanced Dockerfile with security hardening
    local dockerfile="$profile_dir/Dockerfile"
    cat > "$dockerfile" << 'EOF'
FROM ubuntu:22.04

# Set security-focused build arguments
ARG DEBIAN_FRONTEND=noninteractive
ARG USER_UID=1000
ARG USER_GID=1000

# Install minimal dependencies with security updates
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates \
    wget \
    gpg \
    software-properties-common \
    libasound2 \
    libgtk-3-0 \
    libxss1 \
    libnss3 \
    libgconf-2-4 \
    libdrm2 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libpangocairo-1.0-0 \
    libatk1.0-0 \
    libcairo-gobject2 \
    libgdk-pixbuf2.0-0 \
    libgbm1 \
    libxkbcommon0 \
    libxkbfile1 \
    && apt-get upgrade -y \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# Install VSCodium with signature verification
RUN wget -qO - https://gitlab.com/paulcarroty/vscodium-deb-rpm-repo/raw/master/pub.gpg \
    | gpg --dearmor \
    | dd of=/usr/share/keyrings/vscodium-archive-keyring.gpg \
    && echo 'deb [ signed-by=/usr/share/keyrings/vscodium-archive-keyring.gpg ] https://download.vscodium.com/debs vscodium main' \
    | tee /etc/apt/sources.list.d/vscodium.list \
    && apt-get update \
    && apt-get install -y --no-install-recommends codium \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# Create non-root user with specific UID/GID
RUN groupadd -g $USER_GID vscodium \
    && useradd -u $USER_UID -g $USER_GID -m -s /bin/bash vscodium \
    && mkdir -p /home/<USER>/.config/VSCodium \
    && chown -R vscodium:vscodium /home/<USER>

# Remove unnecessary packages and clean up
RUN apt-get autoremove -y \
    && apt-get autoclean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/* \
    && rm -rf /root/.cache

# Set up secure directories with proper permissions
RUN mkdir -p /workspace /tmp/vscodium \
    && chown vscodium:vscodium /workspace /tmp/vscodium \
    && chmod 755 /workspace \
    && chmod 700 /tmp/vscodium

# Switch to non-root user
USER vscodium
WORKDIR /workspace

# Set secure environment variables
ENV DISPLAY=:0
ENV HOME=/home/<USER>
ENV TMPDIR=/tmp/vscodium
ENV XDG_RUNTIME_DIR=/tmp/vscodium
ENV XDG_CACHE_HOME=/tmp/vscodium/.cache
ENV XDG_CONFIG_HOME=/home/<USER>/.config
ENV XDG_DATA_HOME=/home/<USER>/.local/share

# Create secure entrypoint script
COPY --chown=vscodium:vscodium entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
EOF

    # Create secure entrypoint script
    local entrypoint_script="$profile_dir/entrypoint.sh"
    cat > "$entrypoint_script" << 'EOF'
#!/bin/bash
set -euo pipefail

# Set secure umask
umask 077

# Clear potentially dangerous environment variables
unset SSH_AUTH_SOCK SSH_AGENT_PID GPG_AGENT_INFO

# Set resource limits
ulimit -c 0          # No core dumps
ulimit -f 1048576    # Max file size 1GB
ulimit -n 1024       # Max open files
ulimit -u 512        # Max processes
# Virtual memory limit (macOS compatible)
if [[ "$(uname)" == "Darwin" ]]; then
    ulimit -d 2097152 2>/dev/null || true  # Max data segment 2GB (macOS)
else
    ulimit -v 2097152 2>/dev/null || true  # Max virtual memory 2GB (Linux)
fi

# Ensure secure permissions on runtime directories
chmod 700 "$TMPDIR" "$XDG_RUNTIME_DIR" 2>/dev/null || true

# Launch VSCodium with security options
exec codium \
    --no-sandbox \
    --disable-dev-shm-usage \
    --disable-gpu-sandbox \
    --disable-software-rasterizer \
    --disable-background-timer-throttling \
    --disable-backgrounding-occluded-windows \
    --disable-renderer-backgrounding \
    --disable-features=TranslateUI,VizDisplayCompositor \
    "$@"
EOF
    chmod +x "$entrypoint_script"

    # Create seccomp profile for enhanced security
    local seccomp_profile="$profile_dir/seccomp.json"
    cat > "$seccomp_profile" << 'EOF'
{
    "defaultAction": "SCMP_ACT_ERRNO",
    "architectures": ["SCMP_ARCH_X86_64", "SCMP_ARCH_X86", "SCMP_ARCH_X32"],
    "syscalls": [
        {
            "names": [
                "accept", "accept4", "access", "adjtimex", "alarm", "bind", "brk", "capget", "capset",
                "chdir", "chmod", "chown", "chroot", "clock_getres", "clock_gettime", "clock_nanosleep",
                "close", "connect", "copy_file_range", "creat", "dup", "dup2", "dup3", "epoll_create",
                "epoll_create1", "epoll_ctl", "epoll_wait", "eventfd", "eventfd2", "execve", "exit",
                "exit_group", "faccessat", "fadvise64", "fallocate", "fanotify_mark", "fchdir", "fchmod",
                "fchmodat", "fchown", "fchownat", "fcntl", "fdatasync", "fgetxattr", "flistxattr",
                "flock", "fork", "fremovexattr", "fsetxattr", "fstat", "fstatfs", "fsync", "ftruncate",
                "futex", "getcwd", "getdents", "getdents64", "getegid", "geteuid", "getgid", "getgroups",
                "getitimer", "getpeername", "getpgid", "getpgrp", "getpid", "getppid", "getpriority",
                "getrandom", "getresgid", "getresuid", "getrlimit", "getrusage", "getsid", "getsockname",
                "getsockopt", "gettid", "gettimeofday", "getuid", "getxattr", "inotify_add_watch",
                "inotify_init", "inotify_init1", "inotify_rm_watch", "ioctl", "kill", "lchown", "link",
                "linkat", "listen", "listxattr", "llistxattr", "lremovexattr", "lseek", "lsetxattr",
                "lstat", "madvise", "memfd_create", "mkdir", "mkdirat", "mknod", "mknodat", "mlock",
                "mlockall", "mmap", "mount", "mprotect", "mq_getsetattr", "mq_notify", "mq_open",
                "mq_timedreceive", "mq_timedsend", "mq_unlink", "mremap", "msync", "munlock",
                "munlockall", "munmap", "nanosleep", "newfstatat", "open", "openat", "pause", "pipe",
                "pipe2", "poll", "ppoll", "prctl", "pread64", "preadv", "prlimit64", "pselect6",
                "ptrace", "pwrite64", "pwritev", "read", "readahead", "readlink", "readlinkat", "readv",
                "recv", "recvfrom", "recvmsg", "removexattr", "rename", "renameat", "renameat2",
                "restart_syscall", "rmdir", "rt_sigaction", "rt_sigpending", "rt_sigprocmask",
                "rt_sigqueueinfo", "rt_sigreturn", "rt_sigsuspend", "rt_sigtimedwait", "rt_tgsigqueueinfo",
                "sched_getaffinity", "sched_getattr", "sched_getparam", "sched_get_priority_max",
                "sched_get_priority_min", "sched_getscheduler", "sched_setaffinity", "sched_setattr",
                "sched_setparam", "sched_setscheduler", "sched_yield", "seccomp", "select", "semctl",
                "semget", "semop", "send", "sendfile", "sendmsg", "sendto", "setfsgid", "setfsuid",
                "setgid", "setgroups", "setitimer", "setpgid", "setpriority", "setregid", "setresgid",
                "setresuid", "setreuid", "setrlimit", "setsid", "setsockopt", "setuid", "setxattr",
                "shmat", "shmctl", "shmdt", "shmget", "shutdown", "sigaltstack", "signalfd", "signalfd4",
                "socket", "socketpair", "splice", "stat", "statfs", "symlink", "symlinkat", "sync",
                "sync_file_range", "syncfs", "sysinfo", "tee", "tgkill", "time", "timer_create",
                "timer_delete", "timer_getoverrun", "timer_gettime", "timer_settime", "times", "tkill",
                "truncate", "umask", "uname", "unlink", "unlinkat", "utime", "utimensat", "utimes",
                "vfork", "vmsplice", "wait4", "waitid", "write", "writev"
            ],
            "action": "SCMP_ACT_ALLOW"
        }
    ]
}
EOF

    # Build container image with enhanced security
    local image_name="vscodium-sandbox:$profile_name"
    log_info "Building enhanced sandbox container image..."

    # Use BuildKit for better security and caching
    export DOCKER_BUILDKIT=1

    if ! $container_cmd build \
        --no-cache \
        --pull \
        --build-arg USER_UID="$(id -u)" \
        --build-arg USER_GID="$(id -g)" \
        -t "$image_name" \
        -f "$dockerfile" \
        "$profile_dir" &> "$profile_dir/build.log"; then
        log_error "Failed to build container image"
        if [[ -f "$profile_dir/build.log" ]]; then
            log_error "Build log:"
            tail -20 "$profile_dir/build.log"
        fi
        return 1
    fi

    # Prepare enhanced container run arguments
    local container_args=(
        "run"
        "--rm"
        "--init"
        "--name" "vscodium-$profile_name"
        "--hostname" "vscodium-sandbox"
        "--user" "$(id -u):$(id -g)"
        "--security-opt" "no-new-privileges:true"
        "--security-opt" "seccomp=$seccomp_profile"
    )

    # Add AppArmor/SELinux if available
    if command -v aa-status &> /dev/null && aa-status --enabled &> /dev/null; then
        container_args+=("--security-opt" "apparmor:docker-default")
    fi
    if command -v getenforce &> /dev/null && [[ "$(getenforce 2>/dev/null)" == "Enforcing" ]]; then
        container_args+=("--security-opt" "label=type:container_t")
    fi

    # Environment variables
    container_args+=(
        "-e" "DISPLAY=${DISPLAY:-:0}"
        "-e" "XDG_RUNTIME_DIR=/tmp/vscodium"
        "-e" "TMPDIR=/tmp/vscodium"
    )

    # Volume mounts with security options
    container_args+=(
        "-v" "/tmp/.X11-unix:/tmp/.X11-unix:ro"
        "-v" "$user_data_dir:/home/<USER>/.config/VSCodium:rw,Z"
        "-v" "$extensions_dir:/home/<USER>/.vscode-oss/extensions:rw,Z"
        "-v" "$WORKSPACE_PATH:/workspace:rw,Z"
    )

    # Apply network isolation with enhanced security
    case "$NETWORK_ISOLATION" in
        block)
            container_args+=("--network" "none")
            log_info "Network completely blocked"
            ;;
        restrict)
            # Create custom network with restrictions
            local network_name="vscodium-restricted-$profile_name"
            if ! $container_cmd network ls | grep -q "$network_name"; then
                $container_cmd network create \
                    --driver bridge \
                    --internal \
                    --subnet 172.20.0.0/24 \
                    "$network_name" &> /dev/null || true
            fi
            container_args+=("--network" "$network_name")
            log_info "Network restricted to internal bridge"
            ;;
        allow)
            container_args+=("--network" "bridge")
            log_info "Network access allowed"
            ;;
    esac

    # Apply comprehensive resource limits
    if [[ "$RESOURCE_LIMITS" == "true" ]]; then
        container_args+=(
            "--memory" "2g"
            "--memory-swap" "2g"
            "--memory-swappiness" "0"
            "--cpus" "2.0"
            "--cpu-shares" "1024"
            "--pids-limit" "1024"
            "--ulimit" "nofile=1024:1024"
            "--ulimit" "nproc=512:512"
            "--ulimit" "fsize=1073741824:1073741824"
            "--ulimit" "core=0:0"
        )
        log_info "Resource limits applied: 2GB RAM, 2 CPUs, 1024 PIDs"
    fi

    # Add comprehensive security options based on sandbox level
    case "$SANDBOX_LEVEL" in
        maximum)
            container_args+=(
                "--cap-drop" "ALL"
                "--read-only"
                "--tmpfs" "/tmp/vscodium:rw,noexec,nosuid,size=100m"
                "--tmpfs" "/var/tmp:rw,noexec,nosuid,size=50m"
                "--device-cgroup-rule" "a *:* rmw"
            )
            log_info "Maximum security: all capabilities dropped, read-only filesystem"
            ;;
        strict)
            container_args+=(
                "--cap-drop" "ALL"
                "--cap-add" "DAC_OVERRIDE"
                "--cap-add" "SETGID"
                "--cap-add" "SETUID"
                "--tmpfs" "/tmp/vscodium:rw,noexec,nosuid,size=200m"
            )
            log_info "Strict security: minimal capabilities, restricted filesystem"
            ;;
        basic)
            container_args+=(
                "--cap-drop" "NET_RAW"
                "--cap-drop" "SYS_ADMIN"
                "--cap-drop" "SYS_PTRACE"
                "--cap-drop" "AUDIT_WRITE"
            )
            log_info "Basic security: dangerous capabilities dropped"
            ;;
    esac

    # Add container image and VSCodium arguments
    container_args+=("$image_name")
    container_args+=(
        "--user-data-dir" "/home/<USER>/.config/VSCodium"
        "--extensions-dir" "/home/<USER>/.vscode-oss/extensions"
        "--disable-telemetry"
        "--disable-updates"
        "--disable-crash-reporter"
        "--new-window"
        "--disable-workspace-trust"
        "/workspace"
    )

    log_info "Starting enhanced containerized VSCodium..."
    log_info "Security level: $SANDBOX_LEVEL"
    log_info "Network isolation: $NETWORK_ISOLATION"
    log_info "Resource limits: $RESOURCE_LIMITS"

    # Set up cleanup trap
    local cleanup_script="$profile_dir/cleanup.sh"
    cat > "$cleanup_script" << EOF
#!/bin/bash
# Cleanup script for Docker sandbox
set -e

log_info() { echo -e "\033[0;34mℹ️  \$1\033[0m"; }

log_info "Cleaning up Docker sandbox resources..."

# Stop and remove container
if $container_cmd ps -q -f name=vscodium-$profile_name | grep -q .; then
    $container_cmd stop vscodium-$profile_name &> /dev/null || true
    $container_cmd rm vscodium-$profile_name &> /dev/null || true
fi

# Remove custom network if created
if [[ "$NETWORK_ISOLATION" == "restrict" ]]; then
    local network_name="vscodium-restricted-$profile_name"
    if $container_cmd network ls | grep -q "\$network_name"; then
        $container_cmd network rm "\$network_name" &> /dev/null || true
    fi
fi

# Remove image if requested
if [[ "\${1:-}" == "--remove-image" ]]; then
    $container_cmd rmi "$image_name" &> /dev/null || true
fi

log_info "Docker sandbox cleanup completed"
EOF
    chmod +x "$cleanup_script"

    # Set up signal handlers for cleanup
    trap "$cleanup_script" EXIT INT TERM

    # Launch container with comprehensive monitoring
    log_info "Executing: $container_cmd ${container_args[*]}"
    exec $container_cmd "${container_args[@]}"
}

# Enhanced Linux namespace sandbox implementation with comprehensive security
launch_with_namespace_sandbox() {
    local profile_dir="$1"
    local user_data_dir="$profile_dir/user-data"
    local extensions_dir="$profile_dir/extensions"
    local profile_name=$(basename "$profile_dir")

    log_step "Launching VSCodium in enhanced Linux namespace sandbox..."

    # Validate required tools
    if ! command -v unshare &> /dev/null; then
        log_error "unshare command not found. Install util-linux package."
        return 1
    fi

    # Check for required capabilities
    if [[ $EUID -ne 0 ]] && ! unshare --user --map-root-user true &> /dev/null; then
        log_error "User namespaces not available or not permitted"
        log_info "Try: echo 1 | sudo tee /proc/sys/kernel/unprivileged_userns_clone"
        return 1
    fi

    # Create secure sandbox environment
    local sandbox_root="$profile_dir/sandbox-root"
    local sandbox_script="$profile_dir/namespace-sandbox.sh"

    # Prepare sandbox root directory
    mkdir -p "$sandbox_root"/{bin,lib,lib64,usr,etc,tmp,proc,sys,dev,workspace,home}
    mkdir -p "$sandbox_root/home/<USER>"

    # Create essential device nodes
    if [[ "$SANDBOX_LEVEL" != "maximum" ]]; then
        mknod "$sandbox_root/dev/null" c 1 3 2>/dev/null || true
        mknod "$sandbox_root/dev/zero" c 1 5 2>/dev/null || true
        mknod "$sandbox_root/dev/random" c 1 8 2>/dev/null || true
        mknod "$sandbox_root/dev/urandom" c 1 9 2>/dev/null || true
        chmod 666 "$sandbox_root/dev"/{null,zero,random,urandom} 2>/dev/null || true
    fi

    # Create minimal /etc files
    cat > "$sandbox_root/etc/passwd" << EOF
root:x:0:0:root:/root:/bin/bash
vscodium:x:$(id -u):$(id -g):VSCodium User:/home/<USER>/bin/bash
EOF

    cat > "$sandbox_root/etc/group" << EOF
root:x:0:
vscodium:x:$(id -g):
EOF

    echo "vscodium-sandbox" > "$sandbox_root/etc/hostname"

    cat > "$sandbox_root/etc/hosts" << EOF
127.0.0.1 localhost vscodium-sandbox
::1 localhost vscodium-sandbox
EOF

    # Create comprehensive sandbox script with proper escaping
    cat > "$sandbox_script" << 'SANDBOX_SCRIPT_EOF'
#!/bin/bash
set -euo pipefail

# Sandbox configuration (passed as environment variables)
PROFILE_DIR="${SANDBOX_PROFILE_DIR}"
USER_DATA_DIR="${SANDBOX_USER_DATA_DIR}"
EXTENSIONS_DIR="${SANDBOX_EXTENSIONS_DIR}"
WORKSPACE_PATH="${SANDBOX_WORKSPACE_PATH}"
VSCODIUM_CMD="${SANDBOX_VSCODIUM_CMD}"
SANDBOX_ROOT="${SANDBOX_SANDBOX_ROOT}"
SANDBOX_LEVEL="${SANDBOX_SANDBOX_LEVEL}"
NETWORK_ISOLATION="${SANDBOX_NETWORK_ISOLATION}"
RESOURCE_LIMITS="${SANDBOX_RESOURCE_LIMITS}"

log_info() { echo -e "\033[0;34mℹ️  $1\033[0m" >&2; }
log_error() { echo -e "\033[0;31m❌ $1\033[0m" >&2; }

# Function to setup network isolation
setup_network_isolation() {
    case "$NETWORK_ISOLATION" in
        block)
            log_info "Blocking all network access"
            # Create new network namespace with no interfaces
            if command -v ip &> /dev/null; then
                # Bring down all interfaces except loopback
                for iface in $(ip link show | grep -E '^[0-9]+:' | cut -d: -f2 | tr -d ' ' | grep -v lo); do
                    ip link set "$iface" down 2>/dev/null || true
                done
            fi
            ;;
        restrict)
            log_info "Restricting network to localhost only"
            # Keep only loopback interface active
            if command -v ip &> /dev/null; then
                for iface in $(ip link show | grep -E '^[0-9]+:' | cut -d: -f2 | tr -d ' ' | grep -v lo); do
                    ip link set "$iface" down 2>/dev/null || true
                done
                ip link set lo up 2>/dev/null || true
            fi
            ;;
        allow)
            log_info "Allowing full network access"
            ;;
    esac
}

# Function to setup filesystem isolation
setup_filesystem_isolation() {
    log_info "Setting up filesystem isolation"

    # Create new mount namespace
    mount --make-rprivate /

    # Bind mount essential directories
    for dir in /bin /lib /lib64 /usr; do
        if [[ -d "$dir" ]]; then
            mkdir -p "$SANDBOX_ROOT$dir"
            mount --bind "$dir" "$SANDBOX_ROOT$dir"
            if [[ "$SANDBOX_LEVEL" == "maximum" ]] || [[ "$SANDBOX_LEVEL" == "strict" ]]; then
                mount -o remount,ro,bind "$SANDBOX_ROOT$dir"
            fi
        fi
    done

    # Mount proc and sys
    mount -t proc proc "$SANDBOX_ROOT/proc"
    mount -t sysfs sysfs "$SANDBOX_ROOT/sys"

    # Mount dev with restrictions
    if [[ "$SANDBOX_LEVEL" == "maximum" ]]; then
        mount -t tmpfs -o nodev,nosuid,noexec,mode=755 tmpfs "$SANDBOX_ROOT/dev"
        # Only create essential device nodes
        mknod "$SANDBOX_ROOT/dev/null" c 1 3
        mknod "$SANDBOX_ROOT/dev/zero" c 1 5
        chmod 666 "$SANDBOX_ROOT/dev"/{null,zero}
    else
        mount --bind /dev "$SANDBOX_ROOT/dev"
        if [[ "$SANDBOX_LEVEL" == "strict" ]]; then
            mount -o remount,ro,bind "$SANDBOX_ROOT/dev"
        fi
    fi

    # Mount tmp with restrictions
    mount -t tmpfs -o nodev,nosuid,noexec,mode=1777,size=500m tmpfs "$SANDBOX_ROOT/tmp"

    # Bind mount application directories
    mkdir -p "$SANDBOX_ROOT/home/<USER>/.config/VSCodium"
    mkdir -p "$SANDBOX_ROOT/home/<USER>/.vscode-oss/extensions"
    mount --bind "$USER_DATA_DIR" "$SANDBOX_ROOT/home/<USER>/.config/VSCodium"
    mount --bind "$EXTENSIONS_DIR" "$SANDBOX_ROOT/home/<USER>/.vscode-oss/extensions"
    mount --bind "$WORKSPACE_PATH" "$SANDBOX_ROOT/workspace"

    # Change root to sandbox
    chroot "$SANDBOX_ROOT" /bin/bash -c "
        export HOME=/home/<USER>
        export USER=vscodium
        export LOGNAME=vscodium
        export SHELL=/bin/bash
        export PATH=/usr/bin:/bin:/usr/local/bin
        export DISPLAY=\"\${DISPLAY:-:0}\"
        export XDG_RUNTIME_DIR=/tmp
        export TMPDIR=/tmp

        # Set secure umask
        umask 077

        # Clear dangerous environment variables
        unset SSH_AUTH_SOCK SSH_AGENT_PID GPG_AGENT_INFO

        # Change to vscodium user context
        cd /home/<USER>

        # Launch VSCodium with security options
        exec \"$VSCODIUM_CMD\" \
            --user-data-dir=/home/<USER>/.config/VSCodium \
            --extensions-dir=/home/<USER>/.vscode-oss/extensions \
            --disable-telemetry \
            --disable-updates \
            --disable-crash-reporter \
            --disable-dev-shm-usage \
            --disable-gpu-sandbox \
            --no-sandbox \
            --new-window \
            --disable-workspace-trust \
            /workspace
    "
}

# Function to setup resource limits
setup_resource_limits() {
    if [[ "$RESOURCE_LIMITS" == "true" ]]; then
        log_info "Applying resource limits"

        # Set comprehensive ulimits
        ulimit -c 0          # No core dumps
        ulimit -f 1048576    # Max file size 1GB
        ulimit -n 1024       # Max open files
        ulimit -u 512        # Max processes
        ulimit -s 8192       # Max stack size 8MB
        ulimit -t 3600       # Max CPU time 1 hour

        # Virtual memory limit (macOS compatible)
        if [[ "$(uname)" == "Darwin" ]]; then
            ulimit -d 2097152 2>/dev/null || true  # Max data segment 2GB (macOS)
        else
            ulimit -v 2097152 2>/dev/null || true  # Max virtual memory 2GB (Linux)
        fi

        # Set process priority
        renice 10 $$ 2>/dev/null || true
    fi
}

# Main sandbox execution
main() {
    log_info "Starting enhanced namespace sandbox"

    # Setup resource limits first
    setup_resource_limits

    # Create comprehensive namespace isolation
    exec unshare \
        --mount \
        --uts \
        --ipc \
        --pid \
        --net \
        --user \
        --map-root-user \
        --fork \
        --mount-proc \
        bash -c "
            # Setup network isolation
            setup_network_isolation

            # Setup filesystem isolation
            setup_filesystem_isolation
        "
}

# Export functions for use in subshells
export -f setup_network_isolation
export -f setup_filesystem_isolation
export -f setup_resource_limits

# Run main function
main
SANDBOX_SCRIPT_EOF

    chmod +x "$sandbox_script"

    # Set environment variables for the sandbox script (properly escaped)
    export SANDBOX_PROFILE_DIR="$profile_dir"
    export SANDBOX_USER_DATA_DIR="$user_data_dir"
    export SANDBOX_EXTENSIONS_DIR="$extensions_dir"
    export SANDBOX_WORKSPACE_PATH="$WORKSPACE_PATH"
    export SANDBOX_VSCODIUM_CMD="$VSCODIUM_CMD"
    export SANDBOX_SANDBOX_ROOT="$sandbox_root"
    export SANDBOX_SANDBOX_LEVEL="$SANDBOX_LEVEL"
    export SANDBOX_NETWORK_ISOLATION="$NETWORK_ISOLATION"
    export SANDBOX_RESOURCE_LIMITS="$RESOURCE_LIMITS"

    # Create cleanup script
    local cleanup_script="$profile_dir/namespace-cleanup.sh"
    cat > "$cleanup_script" << EOF
#!/bin/bash
set -e

log_info() { echo -e "\033[0;34mℹ️  \$1\033[0m"; }

log_info "Cleaning up namespace sandbox..."

# Kill any remaining processes in the namespace
pkill -f "$profile_name" 2>/dev/null || true

# Cleanup mount points
if mountpoint -q "$sandbox_root/proc" 2>/dev/null; then
    umount "$sandbox_root/proc" 2>/dev/null || true
fi
if mountpoint -q "$sandbox_root/sys" 2>/dev/null; then
    umount "$sandbox_root/sys" 2>/dev/null || true
fi
if mountpoint -q "$sandbox_root/dev" 2>/dev/null; then
    umount "$sandbox_root/dev" 2>/dev/null || true
fi

# Cleanup bind mounts
for mount_point in \$(mount | grep "$sandbox_root" | awk '{print \$3}' | sort -r); do
    umount "\$mount_point" 2>/dev/null || true
done

# Remove sandbox root
rm -rf "$sandbox_root" 2>/dev/null || true

log_info "Namespace sandbox cleanup completed"
EOF
    chmod +x "$cleanup_script"

    # Set up signal handlers for cleanup
    trap "$cleanup_script" EXIT INT TERM

    log_info "Starting enhanced namespace-sandboxed VSCodium..."
    log_info "Sandbox root: $sandbox_root"
    log_info "Security level: $SANDBOX_LEVEL"
    log_info "Network isolation: $NETWORK_ISOLATION"
    log_info "Resource limits: $RESOURCE_LIMITS"

    # Execute sandbox script
    exec "$sandbox_script"
}

# Enhanced macOS sandbox implementation with comprehensive security
launch_with_macos_sandbox() {
    local profile_dir="$1"
    local user_data_dir="$profile_dir/user-data"
    local extensions_dir="$profile_dir/extensions"
    local profile_name=$(basename "$profile_dir")

    log_step "Launching VSCodium in enhanced macOS sandbox..."

    # Validate sandbox-exec availability
    if ! command -v sandbox-exec &> /dev/null; then
        log_error "sandbox-exec not found. This should be available on macOS by default."
        return 1
    fi

    # Create comprehensive sandbox profile
    local sandbox_profile="$profile_dir/enhanced-sandbox.sb"
    cat > "$sandbox_profile" << EOF
(version 1)
(deny default)
(import "system.sb")

; === BASIC SYSTEM OPERATIONS ===
(allow process-info* (target self))
(allow process-info-pidinfo (target self))
(allow process-info-setcontrol (target self))
(allow process-fork)
(allow process-exec
    (literal "$VSCODIUM_CMD")
    (subpath "/usr/bin")
    (subpath "/bin"))

; === SIGNAL HANDLING ===
(allow signal (target self))
(allow signal (target children))

; === MEMORY MANAGEMENT ===
(allow system-audit)
(allow mach-lookup
    (global-name "com.apple.system.logger")
    (global-name "com.apple.system.notification_center"))

; === FILE SYSTEM ACCESS ===
; Allow read access to system libraries and frameworks
(allow file-read*
    (subpath "/System/Library")
    (subpath "/usr/lib")
    (subpath "/usr/share")
    (subpath "/usr/bin")
    (subpath "/bin")
    (subpath "/Library/Frameworks")
    (subpath "/Library/Application Support")
    (literal "/etc/localtime")
    (literal "/etc/passwd")
    (literal "/etc/group")
    (literal "/var/db/timezone/localtime")
    (regex #"^/usr/share/zoneinfo/"))

; Allow execution of essential system utilities
(allow process-exec
    (literal "/usr/bin/dirname")
    (literal "/bin/dirname")
    (literal "/usr/bin/basename")
    (literal "/bin/basename")
    (literal "/usr/bin/readlink")
    (literal "/bin/readlink")
    (literal "/usr/bin/which")
    (literal "/bin/which"))

; Allow read/write access to application directories
(allow file-read* file-write*
    (subpath "$user_data_dir")
    (subpath "$extensions_dir")
    (subpath "$WORKSPACE_PATH"))

; Allow temporary file access
(allow file-read* file-write*
    (subpath "/tmp")
    (subpath "/var/tmp")
    (regex #"^/private/tmp/")
    (regex #"^/private/var/tmp/"))

; Allow access to essential device files
(allow file-read* file-write*
    (literal "/dev/null")
    (literal "/dev/zero")
    (literal "/dev/random")
    (literal "/dev/urandom")
    (literal "/dev/stdin")
    (literal "/dev/stdout")
    (literal "/dev/stderr"))

EOF

    # Add security level-specific restrictions
    case "$SANDBOX_LEVEL" in
        maximum)
            cat >> "$sandbox_profile" << 'EOF'
; === MAXIMUM SECURITY RESTRICTIONS ===
; Deny most system calls and limit to essential operations only
(deny file-write*
    (subpath "/System")
    (subpath "/usr")
    (subpath "/bin")
    (subpath "/sbin")
    (subpath "/Library/System"))

; Restrict process creation
(deny process-exec
    (with no-log))

; Limit mach services
(deny mach-lookup
    (with no-log))

EOF
            ;;
        strict)
            cat >> "$sandbox_profile" << 'EOF'
; === STRICT SECURITY RESTRICTIONS ===
; Allow limited system access
(allow file-read*
    (subpath "/Applications/VSCodium.app")
    (subpath "/usr/local"))

; Allow limited process execution
(allow process-exec
    (subpath "/usr/local/bin")
    (subpath "/usr/bin")
    (subpath "/bin"))

EOF
            ;;
        basic)
            cat >> "$sandbox_profile" << 'EOF'
; === BASIC SECURITY RESTRICTIONS ===
; Allow broader system access for compatibility
(allow file-read*
    (subpath "/Applications")
    (subpath "/usr/local")
    (subpath "/opt"))

; Allow broader process execution for basic level
(allow process-exec
    (subpath "/Applications")
    (subpath "/usr/local/bin")
    (subpath "/usr/bin")
    (subpath "/bin"))

EOF
            ;;
    esac

    # Add network access rules based on isolation level
    case "$NETWORK_ISOLATION" in
        allow)
            cat >> "$sandbox_profile" << 'EOF'
; === FULL NETWORK ACCESS ===
(allow network*)
(allow system-socket)

EOF
            ;;
        restrict)
            cat >> "$sandbox_profile" << 'EOF'
; === RESTRICTED NETWORK ACCESS ===
; Allow basic network operations but restrict external access
(allow network-outbound (literal "/private/var/run/mDNSResponder"))
(allow network-bind)
(allow system-socket)

EOF
            ;;
        block)
            cat >> "$sandbox_profile" << 'EOF'
; === NETWORK ACCESS BLOCKED ===
(deny network*)
(deny system-socket)

EOF
            ;;
    esac

    # Add GUI and display access
    cat >> "$sandbox_profile" << 'EOF'
; === GUI AND DISPLAY ACCESS ===
; Allow window server access for GUI
(allow mach-lookup
    (global-name "com.apple.windowserver.active")
    (global-name "com.apple.windowserver")
    (global-name "com.apple.CoreServices.coreservicesd")
    (global-name "com.apple.dock.server")
    (global-name "com.apple.pasteboard.1")
    (global-name "com.apple.distributed_notifications@1v3"))

; Allow IOKit access for input devices
(allow iokit-open
    (iokit-user-client-class "IOHIDParamUserClient")
    (iokit-user-client-class "IOHIDEventSystemUserClient")
    (iokit-user-client-class "IOAcceleratorES")
    (iokit-user-client-class "IOSurfaceRootUserClient"))

; Allow Core Graphics and display access
(allow iokit-get-properties)
(allow ipc-posix-shm-read-data
    (ipc-posix-name-regex #"^/tmp/com\.apple\.csseed\."))

; Allow font access
(allow file-read*
    (subpath "/System/Library/Fonts")
    (subpath "/Library/Fonts")
    (subpath "/System/Library/Assets/com_apple_MobileAsset_Font"))

EOF

    # Add resource limits if enabled
    if [[ "$RESOURCE_LIMITS" == "true" ]]; then
        cat >> "$sandbox_profile" << 'EOF'
; === RESOURCE LIMITS ===
; Note: macOS sandbox doesn't directly support resource limits
; These are handled by the wrapper script

EOF
    fi

    # Add final rules
    cat >> "$sandbox_profile" << 'EOF'
; === FINAL SECURITY RULES ===
; Allow essential system services
(allow mach-lookup
    (global-name "com.apple.system.opendirectoryd.libinfo")
    (global-name "com.apple.system.logger"))

; Allow sysctl access for system information
(allow sysctl-read)

EOF

    # Create resource limits wrapper script
    local wrapper_script="$profile_dir/macos-sandbox-wrapper.sh"
    cat > "$wrapper_script" << EOF
#!/bin/bash
set -euo pipefail

# Apply resource limits if enabled
if [[ "$RESOURCE_LIMITS" == "true" ]]; then
    # Set comprehensive ulimits (macOS compatible)
    ulimit -c 0          # No core dumps
    ulimit -f 1048576    # Max file size 1GB
    ulimit -n 1024       # Max open files
    ulimit -u 512        # Max processes
    ulimit -s 8192       # Max stack size 8MB
    ulimit -t 3600       # Max CPU time 1 hour

    # Virtual memory limit (macOS doesn't support -v, use -d for data segment)
    if [[ "\$(uname)" == "Darwin" ]]; then
        ulimit -d 2097152 2>/dev/null || true  # Max data segment 2GB (macOS)
    else
        ulimit -v 2097152 2>/dev/null || true  # Max virtual memory 2GB (Linux)
    fi

    # Set process priority
    renice 10 \$\$ 2>/dev/null || true
fi

# Set secure environment
export TMPDIR="\${TMPDIR:-/tmp}/vscodium-\$\$"
mkdir -p "\$TMPDIR"
chmod 700 "\$TMPDIR"

# Clear potentially dangerous environment variables
unset SSH_AUTH_SOCK SSH_AGENT_PID GPG_AGENT_INFO

# Set secure umask
umask 077

# Launch VSCodium with sandbox
exec sandbox-exec -f "$sandbox_profile" "\$@"
EOF
    chmod +x "$wrapper_script"

    # Create cleanup script
    local cleanup_script="$profile_dir/macos-cleanup.sh"
    cat > "$cleanup_script" << EOF
#!/bin/bash
set -e

log_info() { echo -e "\033[0;34mℹ️  \$1\033[0m"; }

log_info "Cleaning up macOS sandbox..."

# Kill any remaining VSCodium processes for this profile
pkill -f "$profile_name" 2>/dev/null || true

# Clean up temporary directories
if [[ -n "\${TMPDIR:-}" ]] && [[ "\$TMPDIR" =~ /tmp/vscodium- ]]; then
    rm -rf "\$TMPDIR" 2>/dev/null || true
fi

# Clean up any remaining temporary files
find /tmp -name "*vscodium*" -user "\$(whoami)" -mtime +1 -delete 2>/dev/null || true

log_info "macOS sandbox cleanup completed"
EOF
    chmod +x "$cleanup_script"

    # Set up signal handlers for cleanup
    trap "$cleanup_script" EXIT INT TERM

    log_info "Starting enhanced macOS sandboxed VSCodium..."
    log_info "Sandbox profile: $sandbox_profile"
    log_info "Security level: $SANDBOX_LEVEL"
    log_info "Network isolation: $NETWORK_ISOLATION"
    log_info "Resource limits: $RESOURCE_LIMITS"

    # Launch with enhanced sandbox wrapper
    exec "$wrapper_script" "$VSCODIUM_CMD" \
        --user-data-dir="$user_data_dir" \
        --extensions-dir="$extensions_dir" \
        --disable-telemetry \
        --disable-updates \
        --disable-crash-reporter \
        --disable-dev-shm-usage \
        --no-sandbox \
        --new-window \
        --disable-workspace-trust \
        "$WORKSPACE_PATH"
}

# Enhanced Firejail sandbox implementation with comprehensive security
launch_with_firejail_sandbox() {
    local profile_dir="$1"
    local user_data_dir="$profile_dir/user-data"
    local extensions_dir="$profile_dir/extensions"
    local profile_name=$(basename "$profile_dir")

    log_step "Launching VSCodium in enhanced Firejail sandbox..."

    # Validate Firejail availability and version
    if ! command -v firejail &> /dev/null; then
        log_error "Firejail not found. Install with: sudo apt install firejail"
        return 1
    fi

    local firejail_version
    firejail_version=$(firejail --version 2>/dev/null | head -1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' || echo "0.0.0")
    log_info "Using Firejail version: $firejail_version"

    # Create custom Firejail profile
    local firejail_profile="$profile_dir/vscodium.profile"
    cat > "$firejail_profile" << EOF
# Enhanced VSCodium Firejail Profile
# Security level: $SANDBOX_LEVEL
# Network isolation: $NETWORK_ISOLATION

# Include base profile
include globals.local

# Disable dangerous features
noblacklist \${HOME}/.config/VSCodium
noblacklist \${HOME}/.vscode-oss

# Filesystem restrictions
private-dev
private-tmp
private-etc passwd,group,hostname,hosts,nsswitch.conf,resolv.conf,localtime,ssl,ca-certificates

# Process restrictions
caps.drop all
ipc-namespace
nonewprivs
noroot
seccomp
shell none

# Memory and resource restrictions
memory-deny-write-execute
disable-mnt
EOF

    # Add security level-specific restrictions
    case "$SANDBOX_LEVEL" in
        maximum)
            cat >> "$firejail_profile" << 'EOF'
# Maximum security restrictions
apparmor
read-only /usr
read-only /bin
read-only /sbin
read-only /lib
read-only /lib64
read-only /opt
read-only /etc
machine-id
nou2f
nodvd
nocd
notv
nosound
no3d
nodbus
noautopulse
x11 none

EOF
            ;;
        strict)
            cat >> "$firejail_profile" << 'EOF'
# Strict security restrictions
apparmor
read-only /usr
read-only /bin
read-only /sbin
machine-id
nou2f
nodvd
nocd
notv

EOF
            ;;
        basic)
            cat >> "$firejail_profile" << 'EOF'
# Basic security restrictions
machine-id

EOF
            ;;
    esac

    # Add network isolation rules
    case "$NETWORK_ISOLATION" in
        block)
            cat >> "$firejail_profile" << 'EOF'
# Block all network access
net none

EOF
            ;;
        restrict)
            cat >> "$firejail_profile" << 'EOF'
# Restrict network access
netfilter
protocol unix,inet,inet6

EOF
            # Create custom netfilter rules
            local netfilter_rules="$profile_dir/netfilter.rules"
            cat > "$netfilter_rules" << 'EOF'
# Allow loopback
-A INPUT -i lo -j ACCEPT
-A OUTPUT -o lo -j ACCEPT

# Allow established connections
-A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# Block everything else
-A INPUT -j DROP
-A OUTPUT -j DROP
EOF
            echo "netfilter $netfilter_rules" >> "$firejail_profile"
            ;;
        allow)
            cat >> "$firejail_profile" << 'EOF'
# Allow network access
protocol unix,inet,inet6,netlink

EOF
            ;;
    esac

    # Add directory access rules
    cat >> "$firejail_profile" << EOF
# Directory access
whitelist $user_data_dir
whitelist $extensions_dir
whitelist $WORKSPACE_PATH
whitelist /tmp
whitelist \${HOME}/.Xauthority
whitelist \${HOME}/.cache/fontconfig
whitelist /usr/share/fonts
whitelist /usr/share/pixmaps
whitelist /usr/share/applications

# Include local customizations
include vscodium.local
EOF

    # Create resource limits wrapper if needed
    local wrapper_script="$profile_dir/firejail-wrapper.sh"
    cat > "$wrapper_script" << EOF
#!/bin/bash
set -euo pipefail

# Apply resource limits if enabled
if [[ "$RESOURCE_LIMITS" == "true" ]]; then
    # Set comprehensive ulimits (macOS compatible)
    ulimit -c 0          # No core dumps
    ulimit -f 1048576    # Max file size 1GB
    ulimit -n 1024       # Max open files
    ulimit -u 512        # Max processes
    ulimit -s 8192       # Max stack size 8MB
    ulimit -t 3600       # Max CPU time 1 hour

    # Virtual memory limit (macOS compatible)
    if [[ "\$(uname)" == "Darwin" ]]; then
        ulimit -d 2097152 2>/dev/null || true  # Max data segment 2GB (macOS)
    else
        ulimit -v 2097152 2>/dev/null || true  # Max virtual memory 2GB (Linux)
    fi

    # Set process priority
    renice 10 \$\$ 2>/dev/null || true
fi

# Set secure environment
export TMPDIR="/tmp/firejail-vscodium-\$\$"
mkdir -p "\$TMPDIR"
chmod 700 "\$TMPDIR"

# Clear potentially dangerous environment variables
unset SSH_AUTH_SOCK SSH_AGENT_PID GPG_AGENT_INFO

# Set secure umask
umask 077

# Execute Firejail with custom profile
exec "\$@"
EOF
    chmod +x "$wrapper_script"

    # Prepare comprehensive Firejail arguments
    local firejail_args=(
        "--profile=$firejail_profile"
        "--name=vscodium-$profile_name"
        "--hostname=vscodium-sandbox"
        "--quiet"
    )

    # Add resource limits if enabled
    if [[ "$RESOURCE_LIMITS" == "true" ]]; then
        firejail_args+=(
            "--rlimit-as=2147483648"    # 2GB virtual memory
            "--rlimit-cpu=3600"         # 1 hour CPU time
            "--rlimit-fsize=1073741824" # 1GB max file size
            "--rlimit-nofile=1024"      # Max 1024 open files
            "--rlimit-nproc=512"        # Max 512 processes
            "--rlimit-sigpending=256"   # Max 256 pending signals
        )
    fi

    # Add X11 security if GUI is needed
    if [[ -n "${DISPLAY:-}" ]]; then
        case "$SANDBOX_LEVEL" in
            maximum)
                # No X11 access in maximum security
                ;;
            strict|basic)
                firejail_args+=(
                    "--x11=xvfb"
                    "--xephyr-screen=1024x768"
                )
                ;;
        esac
    fi

    # Create cleanup script
    local cleanup_script="$profile_dir/firejail-cleanup.sh"
    cat > "$cleanup_script" << EOF
#!/bin/bash
set -e

log_info() { echo -e "\033[0;34mℹ️  \$1\033[0m"; }

log_info "Cleaning up Firejail sandbox..."

# Kill Firejail sandbox
firejail --shutdown=vscodium-$profile_name 2>/dev/null || true

# Clean up temporary directories
if [[ -n "\${TMPDIR:-}" ]] && [[ "\$TMPDIR" =~ /tmp/firejail-vscodium- ]]; then
    rm -rf "\$TMPDIR" 2>/dev/null || true
fi

# Clean up any remaining temporary files
find /tmp -name "*firejail*vscodium*" -user "\$(whoami)" -mtime +1 -delete 2>/dev/null || true

log_info "Firejail sandbox cleanup completed"
EOF
    chmod +x "$cleanup_script"

    # Set up signal handlers for cleanup
    trap "$cleanup_script" EXIT INT TERM

    log_info "Starting enhanced Firejail sandboxed VSCodium..."
    log_info "Profile: $firejail_profile"
    log_info "Security level: $SANDBOX_LEVEL"
    log_info "Network isolation: $NETWORK_ISOLATION"
    log_info "Resource limits: $RESOURCE_LIMITS"

    # Launch with enhanced Firejail sandbox
    log_info "Executing: $wrapper_script firejail ${firejail_args[*]} $VSCODIUM_CMD ..."

    exec "$wrapper_script" firejail "${firejail_args[@]}" "$VSCODIUM_CMD" \
        --user-data-dir="$user_data_dir" \
        --extensions-dir="$extensions_dir" \
        --disable-telemetry \
        --disable-updates \
        --disable-crash-reporter \
        --disable-dev-shm-usage \
        --no-sandbox \
        --new-window \
        --disable-workspace-trust \
        "$WORKSPACE_PATH"
}

# Launch VSCodium
launch_vscodium() {
    local profile_dir="$1"
    local user_data_dir="$profile_dir/user-data"
    local extensions_dir="$profile_dir/extensions"
    local profile_name=$(basename "$profile_dir")

    # Determine sandbox type and launch accordingly
    if [[ "$SANDBOX_LEVEL" == "none" ]]; then
        log_step "Launching VSCodium without sandbox (profile isolation only)..."
    else
        local selected_sandbox
        selected_sandbox=$(select_sandbox_type "$SANDBOX_TYPE")
        log_step "Launching VSCodium with $selected_sandbox sandbox (level: $SANDBOX_LEVEL)..."

        # Check for existing instance first
        if ! check_existing_instance "$user_data_dir" "$profile_name"; then
            log_success "Using existing VSCodium instance"
            return 0
        fi

        # Launch with appropriate sandbox
        case "$selected_sandbox" in
            docker|podman)
                launch_with_docker_sandbox "$profile_dir"
                return $?
                ;;
            namespace)
                launch_with_namespace_sandbox "$profile_dir"
                return $?
                ;;
            macos)
                launch_with_macos_sandbox "$profile_dir"
                return $?
                ;;
            firejail)
                launch_with_firejail_sandbox "$profile_dir"
                return $?
                ;;
            basic)
                log_info "Using enhanced profile isolation"
                ;;
            *)
                log_warning "Unknown sandbox type '$selected_sandbox', falling back to basic isolation"
                ;;
        esac
    fi

    # Enhanced basic/fallback launch with comprehensive security
    log_step "Launching VSCodium with enhanced basic isolation..."

    # Check for existing instance first
    if ! check_existing_instance "$user_data_dir" "$profile_name"; then
        log_success "Using existing VSCodium instance"
        return 0
    fi

    # Create comprehensive isolation wrapper script
    local wrapper_script="$user_data_dir/enhanced-sandboxed-launcher.sh"
    cat > "$wrapper_script" << EOF
#!/bin/bash
# Enhanced Sandboxed VSCodium Launcher
# Provides comprehensive isolation even without advanced sandbox technologies

set -euo pipefail

# Logging functions
log_info() { echo -e "\033[0;34mℹ️  \$1\033[0m" >&2; }
log_warning() { echo -e "\033[1;33m⚠️  \$1\033[0m" >&2; }
log_error() { echo -e "\033[0;31m❌ \$1\033[0m" >&2; }

log_info "Starting enhanced basic sandbox for VSCodium"

# Create isolated temporary directory
export TMPDIR="/tmp/vscodium-isolated-\$\$"
mkdir -p "\$TMPDIR"
chmod 700 "\$TMPDIR"

# Set comprehensive environment variables for isolation
export VSCODE_PORTABLE="$user_data_dir"
export VSCODE_APPDATA="$user_data_dir"
export VSCODE_LOGS="$user_data_dir/logs"
export XDG_CONFIG_HOME="$user_data_dir"
export XDG_DATA_HOME="$user_data_dir/data"
export XDG_CACHE_HOME="$user_data_dir/cache"
export XDG_RUNTIME_DIR="\$TMPDIR"

# Create required directories
mkdir -p "\$XDG_DATA_HOME" "\$XDG_CACHE_HOME" "\$VSCODE_LOGS"

# Apply comprehensive resource limits if enabled
if [[ "$RESOURCE_LIMITS" == "true" ]]; then
    log_info "Applying resource limits"

    # Set comprehensive ulimits (macOS compatible)
    ulimit -c 0          # No core dumps
    ulimit -f 1048576    # Max file size 1GB
    ulimit -n 1024       # Max open files
    ulimit -u 512        # Max processes
    ulimit -s 8192       # Max stack size 8MB
    ulimit -t 3600       # Max CPU time 1 hour

    # Virtual memory limit (macOS compatible)
    if [[ "\$(uname)" == "Darwin" ]]; then
        ulimit -d 2097152 2>/dev/null || true  # Max data segment 2GB (macOS)
    else
        ulimit -v 2097152 2>/dev/null || true  # Max virtual memory 2GB (Linux)
    fi

    # Set process priority (lower priority)
    renice 10 \$\$ 2>/dev/null || true

    # Set I/O priority (lower priority)
    ionice -c 3 -p \$\$ 2>/dev/null || true
fi

# Set restrictive umask for created files
umask 077

# Clear potentially sensitive environment variables
unset SSH_AUTH_SOCK SSH_AGENT_PID GPG_AGENT_INFO
unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
unset GOOGLE_APPLICATION_CREDENTIALS
unset AZURE_CLIENT_ID AZURE_CLIENT_SECRET AZURE_TENANT_ID

# Set secure PATH (remove potentially dangerous directories)
export PATH="/usr/local/bin:/usr/bin:/bin"

# Apply network restrictions if possible
case "$NETWORK_ISOLATION" in
    block)
        log_info "Network access blocked (basic isolation)"
        # Set environment to block network usage
        export http_proxy="http://127.0.0.1:1"
        export https_proxy="http://127.0.0.1:1"
        export ftp_proxy="http://127.0.0.1:1"
        export no_proxy=""
        ;;
    restrict)
        log_info "Network access restricted to localhost"
        # Set environment to discourage external network usage
        export http_proxy="http://127.0.0.1:8080"
        export https_proxy="http://127.0.0.1:8080"
        export ftp_proxy="http://127.0.0.1:8080"
        export no_proxy="localhost,127.0.0.1"
        ;;
    allow)
        log_info "Network access allowed"
        ;;
esac

# Create process monitoring script
cat > "\$TMPDIR/monitor.sh" << 'MONITOR_EOF'
#!/bin/bash
# Process monitor for sandboxed VSCodium

PARENT_PID=\$1
PROFILE_NAME="$profile_name"
LOG_FILE="$user_data_dir/sandbox-monitor.log"

log_monitor() {
    echo "\$(date '+%Y-%m-%d %H:%M:%S') - \$1" >> "\$LOG_FILE"
}

log_monitor "Starting sandbox monitor for PID \$PARENT_PID"

# Monitor resource usage
while kill -0 "\$PARENT_PID" 2>/dev/null; do
    # Get process info
    if ps -p "\$PARENT_PID" -o pid,ppid,pcpu,pmem,vsz,rss,comm > /dev/null 2>&1; then
        PROC_INFO=\$(ps -p "\$PARENT_PID" -o pcpu,pmem,vsz,rss --no-headers 2>/dev/null || echo "N/A")
        log_monitor "Resource usage: \$PROC_INFO"

        # Check for suspicious activity (with error handling)
        CPU_USAGE=\$(echo "\$PROC_INFO" | awk '{print \$1}' | cut -d. -f1 2>/dev/null || echo "0")
        MEM_USAGE=\$(echo "\$PROC_INFO" | awk '{print \$2}' | cut -d. -f1 2>/dev/null || echo "0")

        if [[ "\$CPU_USAGE" =~ ^[0-9]+\$ ]] && [[ "\$CPU_USAGE" -gt 80 ]]; then
            log_monitor "WARNING: High CPU usage: \$CPU_USAGE%"
        fi

        if [[ "\$MEM_USAGE" =~ ^[0-9]+\$ ]] && [[ "\$MEM_USAGE" -gt 50 ]]; then
            log_monitor "WARNING: High memory usage: \$MEM_USAGE%"
        fi
    fi

    sleep 30
done

log_monitor "Sandbox monitor finished for PID \$PARENT_PID"
MONITOR_EOF

chmod +x "\$TMPDIR/monitor.sh"

# Cleanup function
cleanup() {
    log_info "Cleaning up enhanced basic sandbox"

    # Kill any remaining child processes
    pkill -P \$\$ 2>/dev/null || true

    # Clean up temporary directory
    rm -rf "\$TMPDIR" 2>/dev/null || true

    log_info "Enhanced basic sandbox cleanup completed"
}

# Set up signal handlers
trap cleanup EXIT INT TERM

# Start process monitor in background (only if resource limits enabled)
if [[ "$RESOURCE_LIMITS" == "true" ]]; then
    "\$TMPDIR/monitor.sh" \$\$ &
    MONITOR_PID=\$!
fi

# Set process name to distinguish from system VSCodium
exec -a "🔒 Enhanced Sandboxed VSCodium ($profile_name)" "\$@"
EOF

    # Create inner script for network isolation
    cat > "$wrapper_script-inner" << 'EOF'
#!/bin/bash
# Inner script for network-isolated execution
exec "$@"
EOF
    chmod +x "$wrapper_script" "$wrapper_script-inner"

    # Handle Flatpak command differently
    if [[ "$VSCODIUM_CMD" == "flatpak run com.vscodium.codium" ]]; then
        local cmd=(
            "$wrapper_script"
            flatpak run com.vscodium.codium
            "--user-data-dir=$user_data_dir"
            "--extensions-dir=$extensions_dir"
            "--disable-telemetry"
            "--disable-updates"
            "--disable-crash-reporter"
            "--new-window"
            "--disable-workspace-trust"
        )
    else
        local cmd=(
            "$wrapper_script"
            "$VSCODIUM_CMD"
            "--user-data-dir=$user_data_dir"
            "--extensions-dir=$extensions_dir"
            "--disable-telemetry"
            "--disable-updates"
            "--disable-crash-reporter"
            "--new-window"
            "--disable-workspace-trust"
        )
    fi

    if [[ -d "$WORKSPACE_PATH" ]]; then
        cmd+=("$WORKSPACE_PATH")
    fi

    # Create a lock file to prevent multiple instances
    local lock_file="$user_data_dir/.vscodium-lock"
    echo $$ > "$lock_file"

    # Set up cleanup on exit
    trap "rm -f '$lock_file'" EXIT INT TERM

    log_info "Starting Isolated VSCodium Instance..."
    log_info "Profile: $profile_dir"
    log_info "Workspace: $WORKSPACE_PATH"
    log_info "Privacy Level: $PRIVACY_LEVEL"
    log_info "Command: ${cmd[*]}"
    echo ""

    # Create a unique log file for this instance
    local log_file="$user_data_dir/vscodium-launch.log"

    # Launch VSCodium in background and capture PID
    log_info "Executing: ${cmd[*]}"
    "${cmd[@]}" > "$log_file" 2>&1 &
    local vscodium_pid=$!

    # Wait a moment for VSCodium to start
    sleep 5

    # Try to change window title on macOS after launch
    if [[ "$OSTYPE" == "darwin"* ]]; then
        sleep 2  # Give VSCodium more time to fully load
        osascript -e "
        tell application \"System Events\"
            set frontApp to name of first application process whose frontmost is true
            if frontApp contains \"codium\" or frontApp contains \"VSCodium\" then
                tell application process frontApp
                    set name of window 1 to \"🔒 Isolated VSCodium ($fp_id)\"
                end tell
            end if
        end tell
        " 2>/dev/null || true
    fi

    # Check if VSCodium is still running
    if kill -0 "$vscodium_pid" 2>/dev/null; then
        log_success "Isolated VSCodium launched successfully (PID: $vscodium_pid)"
        log_info "Fingerprint details: $user_data_dir/User/globalStorage/fingerprint.json"
        log_info "Profile directory: $profile_dir"
        log_info "Extensions marketplace: Open VSX Registry (https://open-vsx.org)"
        log_info "Launch log: $log_file"
        log_info "Lock file: $lock_file"
        echo ""
        log_warning "🔒 This VSCodium instance is completely isolated from your system VSCodium"
        log_warning "🔒 It uses a separate profile, extensions, and settings"
        log_warning "🔒 To close this instance, close the VSCodium window or press Ctrl+C in this terminal"

        # Wait for VSCodium to exit
        wait "$vscodium_pid"
        log_info "Isolated VSCodium instance closed"
    else
        log_error "VSCodium failed to start or exited immediately"
        log_error "This might be due to:"
        log_error "  1. Conflicting VSCodium instances"
        log_error "  2. Permission issues"
        log_error "  3. Missing dependencies"
        log_error "  4. Display/GUI environment issues"
        echo ""
        if [[ -f "$log_file" ]]; then
            log_error "Launch log output:"
            cat "$log_file"
        else
            log_error "No launch log found at: $log_file"
        fi
        echo ""
        log_error "Try closing any existing VSCodium instances and run again"
        exit 1
    fi
}

# Main function
main() {
    log_info "🔒 Sandboxed VSCodium Fingerprint Runner"
    echo ""

    if [[ "$LIST_MODE" == "true" ]]; then
        list_fingerprints
        exit 0
    fi

    if [[ "$CLEAN_MODE" == "true" ]]; then
        cleanup
        exit 0
    fi

    # Validate sandbox configuration
    validate_sandbox_config

    # Display sandbox information and perform health checks
    if [[ "$SANDBOX_LEVEL" != "none" ]]; then
        local selected_sandbox
        selected_sandbox=$(select_sandbox_type "$SANDBOX_TYPE")
        local capabilities
        # Use read loop instead of mapfile for macOS compatibility
        capabilities=()
        while IFS= read -r line; do
            capabilities+=("$line")
        done < <(detect_sandbox_capabilities)

        log_info "Sandbox Configuration:"
        log_info "  Level: $SANDBOX_LEVEL"
        log_info "  Type: $selected_sandbox (requested: $SANDBOX_TYPE)"
        log_info "  Network: $NETWORK_ISOLATION"
        log_info "  Resource Limits: $RESOURCE_LIMITS"
        log_info "  Available: ${capabilities[*]}"
        echo ""

        if [[ "$selected_sandbox" != "$SANDBOX_TYPE" && "$SANDBOX_TYPE" != "auto" ]]; then
            log_warning "Requested sandbox type '$SANDBOX_TYPE' not available, using '$selected_sandbox'"
        fi

        # Perform sandbox health check
        if ! check_sandbox_health "$selected_sandbox" "$PROFILES_DIR"; then
            log_error "Sandbox health check failed. Consider using a different sandbox type or fixing the issues."
            exit 1
        fi
    else
        log_warning "Sandbox disabled - using profile isolation only"
        echo ""
    fi

    check_vscodium
    
    local fp_id=""
    if [[ "$GENERATE_NEW" == "true" ]]; then
        # Capture fingerprint ID properly using a more robust method
        fp_id=$(generate_fingerprint 2>/dev/null)

        # Validate the fingerprint ID format and ensure it's not empty
        if [[ -z "$fp_id" ]] || [[ ! "$fp_id" =~ ^fp_[0-9]{8}_[0-9]{6}$ ]]; then
            log_error "Invalid or empty fingerprint ID generated: '$fp_id'"
            log_error "Falling back to timestamp-based ID"
            fp_id="fp_$(date +%Y%m%d_%H%M%S)"
        fi
    elif [[ -n "$FINGERPRINT_ID" ]]; then
        fp_id="$FINGERPRINT_ID"
        if [[ ! -f "$FINGERPRINTS_DIR/${fp_id}.json" ]]; then
            log_error "Fingerprint not found: $fp_id"
            list_fingerprints
            exit 1
        fi
    else
        log_error "No fingerprint specified. Use --new-fingerprint or --fingerprint-id"
        show_help
        exit 1
    fi

    log_info "Using fingerprint: $fp_id"

    # Create profile and capture directory path properly
    local profile_dir
    profile_dir=$(create_profile "$fp_id" 2>/dev/null)

    # Validate profile directory
    if [[ -z "$profile_dir" ]] || [[ ! -d "$profile_dir" ]]; then
        log_error "Failed to create profile directory: '$profile_dir'"
        log_error "Attempting to recreate profile with fallback method..."

        # Fallback: construct the profile directory path manually
        local profile_name="vscodium-fp-${fp_id}"
        profile_dir="$PROFILES_DIR/$profile_name"

        if [[ ! -d "$profile_dir" ]]; then
            log_error "Profile directory does not exist and fallback failed: $profile_dir"
            exit 1
        fi
    fi

    # Launch VSCodium with enhanced sandbox
    local profile_name=$(basename "$profile_dir")

    # Perform security audit if in strict or maximum mode (after profile creation)
    if [[ "$SANDBOX_LEVEL" == "strict" ]] || [[ "$SANDBOX_LEVEL" == "maximum" ]]; then
        local selected_sandbox
        selected_sandbox=$(select_sandbox_type "$SANDBOX_TYPE")
        log_info "Performing security audit for $selected_sandbox sandbox..."
        audit_sandbox_security "$selected_sandbox" "$profile_dir"
    fi

    # Start resource monitoring in background if resource limits are enabled
    if [[ "$RESOURCE_LIMITS" == "true" ]] && [[ "$SANDBOX_LEVEL" != "none" ]]; then
        log_info "Starting background resource monitoring..."
        monitor_sandbox_resources "$profile_name" 1800 &  # Monitor for 30 minutes
        local monitor_pid=$!

        # Set up cleanup for monitor process
        cleanup_monitor() {
            if kill -0 "$monitor_pid" 2>/dev/null; then
                kill "$monitor_pid" 2>/dev/null || true
                log_info "Resource monitoring stopped"
            fi
        }
        trap cleanup_monitor EXIT INT TERM
    fi

    # Launch VSCodium with comprehensive sandbox
    launch_vscodium "$profile_dir"
}

main "$@"
