#!/usr/bin/env bash

# Simple VSCodium Runner with Dynamic Fingerprint Generation
# Runs VSCodium with different fingerprints on each execution

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
FINGERPRINTS_DIR="$PROJECT_ROOT/.fingerprints"
PROFILES_DIR="$PROJECT_ROOT/.vscodium-profiles"
VSCODIUM_CMD=""

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_step() { echo -e "${PURPLE}🔧 $1${NC}"; }

show_help() {
    cat << EOF
Sandboxed VSCodium Fingerprint Runner

USAGE:
    $0 [OPTIONS] [WORKSPACE_PATH]

OPTIONS:
    --new-fingerprint, -n    Generate new fingerprint and run Sandboxed VSCodium
    --fingerprint-id ID      Use specific fingerprint by ID
    --privacy-level LEVEL    Privacy level (low, medium, high, maximum) [default: high]
    --sandbox-level LEVEL    Sandbox level (none, basic, strict, maximum) [default: strict]
    --sandbox-type TYPE      Sandbox type (auto, docker, namespace, macos, firejail) [default: auto]
    --network-isolation NET  Network isolation (allow, restrict, block) [default: restrict]
    --resource-limits        Enable CPU/memory limits [default: enabled]
    --list, -l               List available fingerprints
    --clean, -c              Clean up old profiles and fingerprints
    --help, -h               Show this help

EXAMPLES:
    $0 --new-fingerprint                           # New fingerprint, strict sandbox
    $0 --new-fingerprint --sandbox-level maximum   # Maximum security sandbox
    $0 --fingerprint-id fp_20241201_123456         # Use existing fingerprint
    $0 --sandbox-level none                        # Disable sandboxing
    $0 --sandbox-type docker --network-isolation block  # Docker with no network

SANDBOX LEVELS:
    none     - No sandboxing (profile isolation only)
    basic    - Process isolation + restricted file access
    strict   - + Network restrictions + resource limits
    maximum  - + Full system isolation + minimal permissions

SANDBOX TYPES:
    auto      - Automatically select best available sandbox
    docker    - Container-based isolation (strongest)
    namespace - Linux namespace isolation
    macos     - macOS App Sandbox
    firejail  - Firejail-based sandboxing
    basic     - Enhanced profile isolation

ISOLATION:
    - Profiles are stored locally in: $PROFILES_DIR
    - Fingerprints are stored in: $FINGERPRINTS_DIR
    - Each Sandboxed VSCodium instance runs in complete isolation
    - No interference with your main VSCodium installation
    - Configurable security levels for different use cases

VSCODIUM INSTALLATION:
    macOS (Homebrew): brew install --cask vscodium
    macOS (Manual):   Download from https://vscodium.com/
    Linux (Snap):     snap install codium --classic
    Linux (Flatpak):  flatpak install com.vscodium.codium

SANDBOX DEPENDENCIES:
    Docker:    docker or podman
    Namespace: unshare, mount (util-linux)
    macOS:     sandbox-exec (built-in)
    Firejail:  firejail package

EOF
}

# Parse arguments
GENERATE_NEW=false
FINGERPRINT_ID=""
PRIVACY_LEVEL="high"
SANDBOX_LEVEL="strict"
SANDBOX_TYPE="auto"
NETWORK_ISOLATION="restrict"
RESOURCE_LIMITS=true
LIST_MODE=false
CLEAN_MODE=false
WORKSPACE_PATH="$(pwd)"

while [[ $# -gt 0 ]]; do
    case $1 in
        --new-fingerprint|-n)
            GENERATE_NEW=true
            shift
            ;;
        --fingerprint-id)
            FINGERPRINT_ID="$2"
            shift 2
            ;;
        --privacy-level)
            PRIVACY_LEVEL="$2"
            shift 2
            ;;
        --sandbox-level)
            SANDBOX_LEVEL="$2"
            shift 2
            ;;
        --sandbox-type)
            SANDBOX_TYPE="$2"
            shift 2
            ;;
        --network-isolation)
            NETWORK_ISOLATION="$2"
            shift 2
            ;;
        --resource-limits)
            RESOURCE_LIMITS=true
            shift
            ;;
        --no-resource-limits)
            RESOURCE_LIMITS=false
            shift
            ;;
        --list|-l)
            LIST_MODE=true
            shift
            ;;
        --clean|-c)
            CLEAN_MODE=true
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        -*)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
        *)
            WORKSPACE_PATH="$1"
            shift
            ;;
    esac
done

# Find the correct VSCodium binary
find_vscodium() {
    local vscodium_cmd=""

    # Check common VSCodium locations on macOS
    if [[ -f "/Applications/VSCodium.app/Contents/Resources/app/bin/codium" ]]; then
        vscodium_cmd="/Applications/VSCodium.app/Contents/Resources/app/bin/codium"
    elif [[ -f "/usr/local/bin/codium" ]]; then
        vscodium_cmd="/usr/local/bin/codium"
    elif command -v codium &> /dev/null; then
        vscodium_cmd="codium"
    elif [[ -f "/opt/homebrew/bin/codium" ]]; then
        # Homebrew on Apple Silicon
        vscodium_cmd="/opt/homebrew/bin/codium"
    elif command -v vscodium &> /dev/null; then
        # Alternative command name on some systems
        vscodium_cmd="vscodium"
    elif [[ -f "/snap/bin/codium" ]]; then
        # Snap installation
        vscodium_cmd="/snap/bin/codium"
    elif command -v flatpak &> /dev/null && flatpak list | grep -q "com.vscodium.codium"; then
        # Flatpak installation
        vscodium_cmd="flatpak run com.vscodium.codium"
    fi

    if [[ -z "$vscodium_cmd" ]]; then
        log_error "VSCodium not found!" >&2
        log_info "Please install VSCodium to run isolated instances:" >&2
        log_info "  macOS (Homebrew): brew install --cask vscodium" >&2
        log_info "  macOS (Manual):   Download from https://vscodium.com/" >&2
        log_info "  Linux (Snap):     snap install codium --classic" >&2
        log_info "  Linux (Flatpak):  flatpak install com.vscodium.codium" >&2
        exit 1
    fi

    log_info "Found VSCodium for isolation: $vscodium_cmd" >&2
    echo "$vscodium_cmd"
}

# Check if VSCodium is available
check_vscodium() {
    VSCODIUM_CMD=$(find_vscodium 2>&1 | tail -1)
    if [[ -z "$VSCODIUM_CMD" ]]; then
        log_error "Failed to find VSCodium executable" >&2
        exit 1
    fi
}

# Detect available sandbox capabilities
detect_sandbox_capabilities() {
    local capabilities=()

    # Check for Docker
    if command -v docker &> /dev/null && docker info &> /dev/null; then
        capabilities+=("docker")
    elif command -v podman &> /dev/null && podman info &> /dev/null; then
        capabilities+=("podman")
    fi

    # Check for Linux namespaces
    if [[ "$OSTYPE" == "linux-gnu"* ]] && command -v unshare &> /dev/null; then
        capabilities+=("namespace")
    fi

    # Check for macOS sandbox
    if [[ "$OSTYPE" == "darwin"* ]] && command -v sandbox-exec &> /dev/null; then
        capabilities+=("macos")
    fi

    # Check for Firejail
    if command -v firejail &> /dev/null; then
        capabilities+=("firejail")
    fi

    # Basic isolation is always available
    capabilities+=("basic")

    printf "%s\n" "${capabilities[@]}"
}

# Select best sandbox type
select_sandbox_type() {
    local requested="$1"
    local capabilities
    mapfile -t capabilities < <(detect_sandbox_capabilities)

    if [[ "$requested" != "auto" ]]; then
        # Check if requested type is available
        for cap in "${capabilities[@]}"; do
            if [[ "$cap" == "$requested" ]] || [[ "$requested" == "docker" && "$cap" == "podman" ]]; then
                echo "$requested"
                return 0
            fi
        done
        log_warning "Requested sandbox type '$requested' not available, falling back to auto-selection"
    fi

    # Auto-select best available sandbox
    for preferred in "docker" "podman" "namespace" "macos" "firejail" "basic"; do
        for cap in "${capabilities[@]}"; do
            if [[ "$cap" == "$preferred" ]]; then
                echo "$preferred"
                return 0
            fi
        done
    done

    echo "basic"
}

# Validate sandbox configuration
validate_sandbox_config() {
    case "$SANDBOX_LEVEL" in
        none|basic|strict|maximum) ;;
        *)
            log_error "Invalid sandbox level: $SANDBOX_LEVEL"
            log_info "Valid levels: none, basic, strict, maximum"
            exit 1
            ;;
    esac

    case "$NETWORK_ISOLATION" in
        allow|restrict|block) ;;
        *)
            log_error "Invalid network isolation: $NETWORK_ISOLATION"
            log_info "Valid options: allow, restrict, block"
            exit 1
            ;;
    esac
}

# List fingerprints
list_fingerprints() {
    log_step "Available fingerprints:"
    
    if [[ ! -d "$FINGERPRINTS_DIR" ]]; then
        log_warning "No fingerprints found. Generate one with --new-fingerprint"
        return
    fi
    
    local count=0
    for fp_file in "$FINGERPRINTS_DIR"/*.json; do
        if [[ -f "$fp_file" ]]; then
            local fp_id=$(basename "$fp_file" .json)
            local created=$(stat -f %Sm "$fp_file" 2>/dev/null || stat -c %y "$fp_file" 2>/dev/null || echo "unknown")
            echo "  📋 $fp_id (created: $created)"
            ((count++))
        fi
    done
    
    if [[ $count -eq 0 ]]; then
        log_warning "No fingerprints found"
    else
        log_success "Found $count fingerprint(s)"
    fi
}

# Clean old data
cleanup() {
    log_step "Cleaning up old data..."

    # Clean old profiles (older than 7 days) from project-local directory
    if [[ -d "$PROFILES_DIR" ]]; then
        log_info "Cleaning profiles older than 7 days from: $PROFILES_DIR"
        find "$PROFILES_DIR" -type d -name "vscodium-fp-*" -mtime +7 -exec rm -rf {} + 2>/dev/null || true
    fi

    # Also clean any old profiles from the legacy /tmp location
    if [[ -d "/tmp/vscodium-fingerprint-profiles" ]]; then
        log_info "Cleaning legacy profiles from: /tmp/vscodium-fingerprint-profiles"
        find "/tmp/vscodium-fingerprint-profiles" -type d -name "vscodium-fp-*" -mtime +7 -exec rm -rf {} + 2>/dev/null || true
    fi

    # Clean old fingerprints (older than 30 days)
    if [[ -d "$FINGERPRINTS_DIR" ]]; then
        log_info "Cleaning fingerprints older than 30 days from: $FINGERPRINTS_DIR"
        find "$FINGERPRINTS_DIR" -name "*.json" -mtime +30 -delete 2>/dev/null || true
    fi

    log_success "Cleanup completed"
}

# Generate simple mock fingerprint (no Rust dependency)
generate_mock_fingerprint() {
    local fp_id="$1"
    local fp_path="$2"

    log_step "Generating mock fingerprint: $fp_id" >&2
    
    mkdir -p "$(dirname "$fp_path")"
    
    # Generate a simple JSON fingerprint with VSCodium-specific metadata
    cat > "$fp_path" << EOF
{
  "id": "$fp_id",
  "version": "1.0.0",
  "created_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "privacy_level": "$PRIVACY_LEVEL",
  "rotation_enabled": true,
  "editor_type": "vscodium",
  "device_id": "$(openssl rand -hex 16)",
  "hardware_signature": {
    "cpu_signature": "mock-cpu-$(openssl rand -hex 4)",
    "memory_class": "8-16GB",
    "architecture": "$(uname -m)",
    "hardware_uuid_hash": "$(openssl rand -hex 16)",
    "performance_class": "high"
  },
  "system_signature": {
    "os_family": "$(uname -s)",
    "os_version_class": "recent",
    "timezone_class": "$(date +%Z)",
    "locale_class": "en-US"
  },
  "vscodium_signature": {
    "version_class": "1.80-1.90",
    "machine_id_hash": "$(openssl rand -hex 16)",
    "session_id_hash": "$(openssl rand -hex 16)",
    "telemetry_disabled": true,
    "marketplace_override": "open-vsx.org"
  },
  "network_signature": {
    "mac_signature": "$(openssl rand -hex 12)",
    "network_class": "ethernet"
  },
  "privacy_metadata": {
    "anonymization_level": "$PRIVACY_LEVEL",
    "data_minimization_applied": true,
    "tracking_protection_enabled": true,
    "microsoft_telemetry_disabled": true
  },
  "fingerprint_hash": "$(openssl rand -hex 32)"
}
EOF
    
    log_success "Mock fingerprint generated: $fp_id" >&2
}

# Try to use Rust tool, fallback to mock
generate_fingerprint() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local fp_id="fp_${timestamp}"
    local fp_path="$FINGERPRINTS_DIR/${fp_id}.json"

    # Try Rust tool first
    if [[ -f "$PROJECT_ROOT/Cargo.toml" ]] && command -v cargo &> /dev/null; then
        log_step "Attempting to use Rust fingerprint generator..." >&2

        cd "$PROJECT_ROOT"
        # Set environment variable to bypass ethics prompt for automated usage
        export SECURITY_TOOLS_ETHICS_ACCEPTED=true
        # Redirect all output to suppress colored logs, only check exit code
        if cargo run --release -p privacy-fingerprint-generator -- \
            --privacy-level "$PRIVACY_LEVEL" \
            --enable-rotation \
            --format json \
            generate \
            --real-system \
            --save-path "$fp_path" >/dev/null 2>&1; then
            log_success "Rust fingerprint generated: $fp_id" >&2
            # Output only the fingerprint ID to stdout (no extra formatting)
            printf "%s" "$fp_id"
            return
        else
            log_warning "Rust tool failed, using mock fingerprint" >&2
        fi
    fi

    # Fallback to mock
    generate_mock_fingerprint "$fp_id" "$fp_path" >&2
    # Output only the fingerprint ID to stdout (no extra formatting)
    printf "%s" "$fp_id"
}

# Create isolated VSCodium profile
create_profile() {
    local fp_id="$1"
    local fp_path="$FINGERPRINTS_DIR/${fp_id}.json"

    local profile_name="vscodium-fp-${fp_id}"
    local profile_dir="$PROFILES_DIR/$profile_name"
    local user_data_dir="$profile_dir/user-data"
    local extensions_dir="$profile_dir/extensions"

    log_step "Creating isolated profile for VSCodium instance: $profile_name" >&2
    log_info "Isolated profile directory: $profile_dir" >&2

    # Create directories
    if ! mkdir -p "$user_data_dir/User/globalStorage"; then
        log_error "Failed to create user data directory: $user_data_dir/User/globalStorage" >&2
        exit 1
    fi

    if ! mkdir -p "$extensions_dir"; then
        log_error "Failed to create extensions directory: $extensions_dir" >&2
        exit 1
    fi

    # Copy fingerprint
    if [[ -f "$fp_path" ]]; then
        if ! cp "$fp_path" "$user_data_dir/User/globalStorage/fingerprint.json"; then
            log_error "Failed to copy fingerprint to profile" >&2
            exit 1
        fi
        log_info "Fingerprint installed: $user_data_dir/User/globalStorage/fingerprint.json" >&2
    else
        log_warning "Fingerprint file not found: $fp_path" >&2
    fi
    
    # Create VSCodium settings with privacy-focused configuration and single window isolation
    cat > "$user_data_dir/User/settings.json" << EOF
{
  "security.workspace.trust.enabled": false,
  "telemetry.telemetryLevel": "off",
  "update.mode": "none",
  "extensions.autoUpdate": false,
  "workbench.enableExperiments": false,
  "workbench.startupEditor": "none",
  "window.restoreWindows": "none",
  "window.newWindowDimensions": "inherit",
  "window.openFilesInNewWindow": "off",
  "window.openFoldersInNewWindow": "off",
  "window.openWithoutArgumentsInNewWindow": "off",
  "window.restoreFullscreen": false,
  "window.titleBarStyle": "custom",
  "window.menuBarVisibility": "toggle",
  "window.title": "🔒 Isolated VSCodium ($fp_id) - \${activeEditorShort}\${separator}\${rootName}",
  "window.titleSeparator": " - ",
  "window.customTitleBarVisibility": "auto",
  "workbench.editor.enablePreview": false,
  "workbench.editor.enablePreviewFromQuickOpen": false,
  "workbench.editor.showTabs": true,
  "workbench.editor.tabCloseButton": "right",
  "workbench.editor.tabSizing": "fit",
  "workbench.editor.wrapTabs": false,
  "workbench.editor.scrollToSwitchTabs": false,
  "workbench.editor.focusRecentEditorAfterClose": true,
  "workbench.editor.limit.enabled": false,
  "workbench.editor.restoreViewState": false,
  "workbench.colorTheme": "Default Dark+",
  "workbench.iconTheme": "vs-seti",
  "workbench.productIconTheme": "Default",
  "privacy.fingerprintIsolation": true,
  "privacy.profileName": "$profile_name",
  "privacy.fingerprintId": "$fp_id",
  "privacy.fingerprintPath": "$user_data_dir/User/globalStorage/fingerprint.json",
  "privacy.privacyLevel": "$PRIVACY_LEVEL",
  "augment.fingerprintOverride": "$user_data_dir/User/globalStorage/fingerprint.json",
  "extensions.gallery.serviceUrl": "https://open-vsx.org/vscode/gallery",
  "extensions.gallery.itemUrl": "https://open-vsx.org/vscode/item",
  "extensions.gallery.searchUrl": "https://open-vsx.org/vscode/search",
  "extensions.gallery.resourceUrlTemplate": "https://open-vsx.org/vscode/unpkg/{publisher}/{name}/{version}/{path}",
  "extensions.gallery.cacheUrl": "https://open-vsx.org/vscode/cache",
  "extensions.gallery.controlUrl": "https://open-vsx.org/vscode/control",
  "extensions.gallery.nlsBaseUrl": "https://www.bing.com/api/v7.0/suggestions",
  "editor.suggestSelection": "first",
  "vsintellicode.modify.editor.suggestSelection": "automaticallyOverrodeDefaultValue"
}
EOF
    
    # Create product.json override for Isolated VSCodium to use Open VSX Registry
    cat > "$user_data_dir/product.json" << EOF
{
  "nameShort": "🔒 Isolated VSCodium",
  "nameLong": "🔒 Isolated VSCodium (Fingerprint: $fp_id)",
  "applicationName": "isolated-vscodium",
  "dataFolderName": ".isolated-vscodium",
  "win32MutexName": "isolated-vscodium-$fp_id",
  "licenseName": "MIT",
  "licenseUrl": "https://github.com/VSCodium/vscodium/blob/master/LICENSE",
  "serverDataFolderName": ".isolated-vscodium-server",
  "serverApplicationName": "isolated-vscodium-server",
  "extensionsGallery": {
    "serviceUrl": "https://open-vsx.org/vscode/gallery",
    "itemUrl": "https://open-vsx.org/vscode/item",
    "searchUrl": "https://open-vsx.org/vscode/search",
    "resourceUrlTemplate": "https://open-vsx.org/vscode/unpkg/{publisher}/{name}/{version}/{path}",
    "cacheUrl": "https://open-vsx.org/vscode/cache",
    "controlUrl": "https://open-vsx.org/vscode/control",
    "nlsBaseUrl": "https://www.bing.com/api/v7.0/suggestions"
  },
  "linkProtectionTrustedDomains": [
    "https://open-vsx.org",
    "https://vscodium.com"
  ],
  "documentationUrl": "https://go.microsoft.com/fwlink/?LinkID=533484#vscodium",
  "requestFeatureUrl": "https://github.com/VSCodium/vscodium/issues",
  "reportIssueUrl": "https://github.com/VSCodium/vscodium/issues",
  "tipsAndTricksUrl": "https://go.microsoft.com/fwlink/?linkid=852118",
  "twitterUrl": "https://twitter.com/code",
  "privacyStatementUrl": "https://vscodium.com/",
  "telemetryOptOutUrl": "https://vscodium.com/"
}
EOF
    
    log_success "Profile created: $profile_dir" >&2
    # Output only the profile directory to stdout (no extra formatting)
    printf "%s" "$profile_dir"
}

# Check for existing Isolated VSCodium instances using this profile
check_existing_instance() {
    local user_data_dir="$1"
    local profile_name="$2"

    # Check if Isolated VSCodium is already running with this profile
    if pgrep -f "$user_data_dir" > /dev/null 2>&1; then
        log_warning "Isolated VSCodium instance already running with profile: $profile_name"
        log_info "Bringing existing window to front instead of creating new one..."

        # Try to bring the existing window to front (macOS specific)
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # Try to activate the isolated instance
            osascript -e 'tell application "IsolatedVSCodium" to activate' 2>/dev/null || \
            osascript -e 'tell application "VSCodium" to activate' 2>/dev/null || true
        fi

        return 1  # Instance already exists
    fi

    return 0  # No existing instance
}

# Docker sandbox implementation
launch_with_docker_sandbox() {
    local profile_dir="$1"
    local user_data_dir="$profile_dir/user-data"
    local extensions_dir="$profile_dir/extensions"
    local profile_name=$(basename "$profile_dir")

    log_step "Launching VSCodium in Docker sandbox..."

    # Determine container runtime
    local container_cmd="docker"
    if command -v podman &> /dev/null && ! command -v docker &> /dev/null; then
        container_cmd="podman"
    fi

    # Create Dockerfile for VSCodium sandbox
    local dockerfile="$profile_dir/Dockerfile"
    cat > "$dockerfile" << 'EOF'
FROM ubuntu:22.04

# Install dependencies
RUN apt-get update && apt-get install -y \
    wget \
    gpg \
    software-properties-common \
    libasound2 \
    libgtk-3-0 \
    libxss1 \
    libnss3 \
    libgconf-2-4 \
    libdrm2 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libasound2 \
    libpangocairo-1.0-0 \
    libatk1.0-0 \
    libcairo-gobject2 \
    libgtk-3-0 \
    libgdk-pixbuf2.0-0 \
    libxss1 \
    libgbm1 \
    && rm -rf /var/lib/apt/lists/*

# Install VSCodium
RUN wget -qO - https://gitlab.com/paulcarroty/vscodium-deb-rpm-repo/raw/master/pub.gpg \
    | gpg --dearmor \
    | dd of=/usr/share/keyrings/vscodium-archive-keyring.gpg \
    && echo 'deb [ signed-by=/usr/share/keyrings/vscodium-archive-keyring.gpg ] https://download.vscodium.com/debs vscodium main' \
    | tee /etc/apt/sources.list.d/vscodium.list \
    && apt-get update \
    && apt-get install -y codium \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd -m -s /bin/bash vscodium

# Set up directories
RUN mkdir -p /home/<USER>/.config/VSCodium \
    && chown -R vscodium:vscodium /home/<USER>

USER vscodium
WORKDIR /workspace

# Set environment variables
ENV DISPLAY=:0
ENV HOME=/home/<USER>

ENTRYPOINT ["codium"]
EOF

    # Build container image
    local image_name="vscodium-sandbox:$profile_name"
    log_info "Building sandbox container image..."
    if ! $container_cmd build -t "$image_name" -f "$dockerfile" "$profile_dir" &> "$profile_dir/build.log"; then
        log_error "Failed to build container image"
        cat "$profile_dir/build.log"
        return 1
    fi

    # Prepare container run arguments
    local container_args=(
        "run"
        "--rm"
        "--name" "vscodium-$profile_name"
        "-e" "DISPLAY=${DISPLAY:-:0}"
        "-v" "/tmp/.X11-unix:/tmp/.X11-unix:rw"
        "-v" "$user_data_dir:/home/<USER>/.config/VSCodium:rw"
        "-v" "$extensions_dir:/home/<USER>/.vscode-oss/extensions:rw"
        "-v" "$WORKSPACE_PATH:/workspace:rw"
    )

    # Apply network isolation
    case "$NETWORK_ISOLATION" in
        block)
            container_args+=("--network" "none")
            ;;
        restrict)
            container_args+=("--network" "bridge")
            # TODO: Add iptables rules for restricted access
            ;;
        allow)
            container_args+=("--network" "host")
            ;;
    esac

    # Apply resource limits
    if [[ "$RESOURCE_LIMITS" == "true" ]]; then
        container_args+=("--memory" "2g")
        container_args+=("--cpus" "2.0")
        container_args+=("--pids-limit" "1024")
    fi

    # Add security options based on sandbox level
    case "$SANDBOX_LEVEL" in
        maximum)
            container_args+=("--security-opt" "no-new-privileges")
            container_args+=("--cap-drop" "ALL")
            container_args+=("--read-only")
            container_args+=("--tmpfs" "/tmp")
            ;;
        strict)
            container_args+=("--security-opt" "no-new-privileges")
            container_args+=("--cap-drop" "ALL")
            container_args+=("--cap-add" "DAC_OVERRIDE")
            ;;
        basic)
            container_args+=("--cap-drop" "NET_RAW")
            ;;
    esac

    container_args+=("$image_name")
    container_args+=("--user-data-dir" "/home/<USER>/.config/VSCodium")
    container_args+=("--extensions-dir" "/home/<USER>/.vscode-oss/extensions")
    container_args+=("--disable-telemetry")
    container_args+=("--disable-updates")
    container_args+=("--new-window")
    container_args+=("/workspace")

    log_info "Starting containerized VSCodium..."
    log_info "Container: $container_cmd ${container_args[*]}"

    # Launch container
    exec $container_cmd "${container_args[@]}"
}

# Linux namespace sandbox implementation
launch_with_namespace_sandbox() {
    local profile_dir="$1"
    local user_data_dir="$profile_dir/user-data"
    local extensions_dir="$profile_dir/extensions"
    local profile_name=$(basename "$profile_dir")

    log_step "Launching VSCodium in Linux namespace sandbox..."

    # Create sandbox script
    local sandbox_script="$profile_dir/namespace-sandbox.sh"
    cat > "$sandbox_script" << EOF
#!/bin/bash
set -euo pipefail

# Create temporary mount namespace
exec unshare --mount --pid --fork --mount-proc \\
    --map-root-user \\
    bash -c '
        # Set up minimal filesystem
        mount --bind "$user_data_dir" "$user_data_dir"
        mount --bind "$extensions_dir" "$extensions_dir"
        mount --bind "$WORKSPACE_PATH" "$WORKSPACE_PATH"

        # Make root filesystem read-only for maximum security
        if [[ "$SANDBOX_LEVEL" == "maximum" ]]; then
            mount -o remount,ro /
        fi

        # Apply network restrictions
        if [[ "$NETWORK_ISOLATION" == "block" ]]; then
            # Block all network access
            iptables -P INPUT DROP 2>/dev/null || true
            iptables -P OUTPUT DROP 2>/dev/null || true
            iptables -P FORWARD DROP 2>/dev/null || true
        elif [[ "$NETWORK_ISOLATION" == "restrict" ]]; then
            # Allow only localhost and essential services
            iptables -P INPUT DROP 2>/dev/null || true
            iptables -P OUTPUT DROP 2>/dev/null || true
            iptables -A OUTPUT -o lo -j ACCEPT 2>/dev/null || true
            iptables -A INPUT -i lo -j ACCEPT 2>/dev/null || true
        fi

        # Set resource limits
        if [[ "$RESOURCE_LIMITS" == "true" ]]; then
            ulimit -v 2097152  # 2GB virtual memory
            ulimit -u 1024     # Max 1024 processes
        fi

        # Launch VSCodium
        exec "$VSCODIUM_CMD" \\
            --user-data-dir="$user_data_dir" \\
            --extensions-dir="$extensions_dir" \\
            --disable-telemetry \\
            --disable-updates \\
            --disable-crash-reporter \\
            --new-window \\
            --disable-workspace-trust \\
            "$WORKSPACE_PATH"
    '
EOF

    chmod +x "$sandbox_script"

    log_info "Starting namespace-sandboxed VSCodium..."
    log_info "Sandbox script: $sandbox_script"

    # Execute sandbox script
    exec "$sandbox_script"
}

# macOS sandbox implementation
launch_with_macos_sandbox() {
    local profile_dir="$1"
    local user_data_dir="$profile_dir/user-data"
    local extensions_dir="$profile_dir/extensions"
    local profile_name=$(basename "$profile_dir")

    log_step "Launching VSCodium in macOS sandbox..."

    # Create sandbox profile
    local sandbox_profile="$profile_dir/sandbox.sb"
    cat > "$sandbox_profile" << EOF
(version 1)
(deny default)

; Allow basic system operations
(allow process-info* (target self))
(allow process-info-pidinfo (target self))
(allow process-info-setcontrol (target self))

; Allow file access to specific directories
(allow file-read* file-write*
    (subpath "$user_data_dir")
    (subpath "$extensions_dir")
    (subpath "$WORKSPACE_PATH")
    (subpath "/System/Library")
    (subpath "/usr/lib")
    (subpath "/usr/share")
    (literal "/dev/null")
    (literal "/dev/random")
    (literal "/dev/urandom"))

; Allow network access based on isolation level
EOF

    case "$NETWORK_ISOLATION" in
        allow)
            echo "(allow network*)" >> "$sandbox_profile"
            ;;
        restrict)
            cat >> "$sandbox_profile" << 'EOF'
(allow network-outbound (remote ip "127.0.0.1"))
(allow network-bind (local ip "127.0.0.1"))
EOF
            ;;
        block)
            echo "(deny network*)" >> "$sandbox_profile"
            ;;
    esac

    # Add display access for GUI
    cat >> "$sandbox_profile" << 'EOF'

; Allow GUI operations
(allow mach-lookup
    (global-name "com.apple.windowserver.active")
    (global-name "com.apple.CoreServices.coreservicesd"))
(allow iokit-open (iokit-user-client-class "IOHIDParamUserClient"))
EOF

    log_info "Starting macOS sandboxed VSCodium..."
    log_info "Sandbox profile: $sandbox_profile"

    # Launch with sandbox-exec
    exec sandbox-exec -f "$sandbox_profile" "$VSCODIUM_CMD" \
        --user-data-dir="$user_data_dir" \
        --extensions-dir="$extensions_dir" \
        --disable-telemetry \
        --disable-updates \
        --disable-crash-reporter \
        --new-window \
        --disable-workspace-trust \
        "$WORKSPACE_PATH"
}

# Firejail sandbox implementation
launch_with_firejail_sandbox() {
    local profile_dir="$1"
    local user_data_dir="$profile_dir/user-data"
    local extensions_dir="$profile_dir/extensions"
    local profile_name=$(basename "$profile_dir")

    log_step "Launching VSCodium in Firejail sandbox..."

    local firejail_args=(
        "--name=vscodium-$profile_name"
        "--private-tmp"
        "--private-dev"
        "--private-etc=passwd,group,hostname,hosts,nsswitch.conf,resolv.conf"
    )

    # Apply network isolation
    case "$NETWORK_ISOLATION" in
        block)
            firejail_args+=("--net=none")
            ;;
        restrict)
            firejail_args+=("--netfilter")
            ;;
        allow)
            # Default network access
            ;;
    esac

    # Apply security restrictions based on sandbox level
    case "$SANDBOX_LEVEL" in
        maximum)
            firejail_args+=(
                "--seccomp"
                "--caps.drop=all"
                "--nonewprivs"
                "--noroot"
                "--read-only=/usr"
                "--read-only=/bin"
                "--read-only=/sbin"
                "--read-only=/lib"
                "--read-only=/lib64"
            )
            ;;
        strict)
            firejail_args+=(
                "--seccomp"
                "--caps.drop=all"
                "--nonewprivs"
            )
            ;;
        basic)
            firejail_args+=("--seccomp")
            ;;
    esac

    # Allow access to required directories
    firejail_args+=(
        "--whitelist=$user_data_dir"
        "--whitelist=$extensions_dir"
        "--whitelist=$WORKSPACE_PATH"
    )

    log_info "Starting Firejail sandboxed VSCodium..."
    log_info "Firejail args: ${firejail_args[*]}"

    # Launch with Firejail
    exec firejail "${firejail_args[@]}" "$VSCODIUM_CMD" \
        --user-data-dir="$user_data_dir" \
        --extensions-dir="$extensions_dir" \
        --disable-telemetry \
        --disable-updates \
        --disable-crash-reporter \
        --new-window \
        --disable-workspace-trust \
        "$WORKSPACE_PATH"
}

# Launch VSCodium
launch_vscodium() {
    local profile_dir="$1"
    local user_data_dir="$profile_dir/user-data"
    local extensions_dir="$profile_dir/extensions"
    local profile_name=$(basename "$profile_dir")

    # Determine sandbox type and launch accordingly
    if [[ "$SANDBOX_LEVEL" == "none" ]]; then
        log_step "Launching VSCodium without sandbox (profile isolation only)..."
    else
        local selected_sandbox
        selected_sandbox=$(select_sandbox_type "$SANDBOX_TYPE")
        log_step "Launching VSCodium with $selected_sandbox sandbox (level: $SANDBOX_LEVEL)..."

        # Check for existing instance first
        if ! check_existing_instance "$user_data_dir" "$profile_name"; then
            log_success "Using existing VSCodium instance"
            return 0
        fi

        # Launch with appropriate sandbox
        case "$selected_sandbox" in
            docker|podman)
                launch_with_docker_sandbox "$profile_dir"
                return $?
                ;;
            namespace)
                launch_with_namespace_sandbox "$profile_dir"
                return $?
                ;;
            macos)
                launch_with_macos_sandbox "$profile_dir"
                return $?
                ;;
            firejail)
                launch_with_firejail_sandbox "$profile_dir"
                return $?
                ;;
            basic)
                log_info "Using enhanced profile isolation"
                ;;
            *)
                log_warning "Unknown sandbox type '$selected_sandbox', falling back to basic isolation"
                ;;
        esac
    fi

    # Basic/fallback launch (original implementation with enhancements)
    log_step "Launching VSCodium with basic isolation..."

    # Check for existing instance first
    if ! check_existing_instance "$user_data_dir" "$profile_name"; then
        log_success "Using existing VSCodium instance"
        return 0
    fi

    # Create a wrapper script for true isolation
    local wrapper_script="$user_data_dir/isolated-codium-launcher.sh"
    cat > "$wrapper_script" << EOF
#!/bin/bash
# Isolated VSCodium Launcher
# This wrapper ensures complete isolation from system VSCodium

# Set environment variables for better isolation
export VSCODE_PORTABLE="$user_data_dir"
export VSCODE_APPDATA="$user_data_dir"
export VSCODE_LOGS="$user_data_dir/logs"

# Set process name to distinguish from system VSCodium
exec -a "🔒 Isolated VSCodium ($fp_id)" "\$@"
EOF
    chmod +x "$wrapper_script"

    # Handle Flatpak command differently
    if [[ "$VSCODIUM_CMD" == "flatpak run com.vscodium.codium" ]]; then
        local cmd=(
            "$wrapper_script"
            flatpak run com.vscodium.codium
            "--user-data-dir=$user_data_dir"
            "--extensions-dir=$extensions_dir"
            "--disable-telemetry"
            "--disable-updates"
            "--disable-crash-reporter"
            "--new-window"
            "--disable-workspace-trust"
        )
    else
        local cmd=(
            "$wrapper_script"
            "$VSCODIUM_CMD"
            "--user-data-dir=$user_data_dir"
            "--extensions-dir=$extensions_dir"
            "--disable-telemetry"
            "--disable-updates"
            "--disable-crash-reporter"
            "--new-window"
            "--disable-workspace-trust"
        )
    fi

    if [[ -d "$WORKSPACE_PATH" ]]; then
        cmd+=("$WORKSPACE_PATH")
    fi

    # Create a lock file to prevent multiple instances
    local lock_file="$user_data_dir/.vscodium-lock"
    echo $$ > "$lock_file"

    # Set up cleanup on exit
    trap "rm -f '$lock_file'" EXIT INT TERM

    log_info "Starting Isolated VSCodium Instance..."
    log_info "Profile: $profile_dir"
    log_info "Workspace: $WORKSPACE_PATH"
    log_info "Privacy Level: $PRIVACY_LEVEL"
    log_info "Command: ${cmd[*]}"
    echo ""

    # Create a unique log file for this instance
    local log_file="$user_data_dir/vscodium-launch.log"

    # Launch VSCodium in background and capture PID
    log_info "Executing: ${cmd[*]}"
    "${cmd[@]}" > "$log_file" 2>&1 &
    local vscodium_pid=$!

    # Wait a moment for VSCodium to start
    sleep 5

    # Try to change window title on macOS after launch
    if [[ "$OSTYPE" == "darwin"* ]]; then
        sleep 2  # Give VSCodium more time to fully load
        osascript -e "
        tell application \"System Events\"
            set frontApp to name of first application process whose frontmost is true
            if frontApp contains \"codium\" or frontApp contains \"VSCodium\" then
                tell application process frontApp
                    set name of window 1 to \"🔒 Isolated VSCodium ($fp_id)\"
                end tell
            end if
        end tell
        " 2>/dev/null || true
    fi

    # Check if VSCodium is still running
    if kill -0 "$vscodium_pid" 2>/dev/null; then
        log_success "Isolated VSCodium launched successfully (PID: $vscodium_pid)"
        log_info "Fingerprint details: $user_data_dir/User/globalStorage/fingerprint.json"
        log_info "Profile directory: $profile_dir"
        log_info "Extensions marketplace: Open VSX Registry (https://open-vsx.org)"
        log_info "Launch log: $log_file"
        log_info "Lock file: $lock_file"
        echo ""
        log_warning "🔒 This VSCodium instance is completely isolated from your system VSCodium"
        log_warning "🔒 It uses a separate profile, extensions, and settings"
        log_warning "🔒 To close this instance, close the VSCodium window or press Ctrl+C in this terminal"

        # Wait for VSCodium to exit
        wait "$vscodium_pid"
        log_info "Isolated VSCodium instance closed"
    else
        log_error "VSCodium failed to start or exited immediately"
        log_error "This might be due to:"
        log_error "  1. Conflicting VSCodium instances"
        log_error "  2. Permission issues"
        log_error "  3. Missing dependencies"
        log_error "  4. Display/GUI environment issues"
        echo ""
        if [[ -f "$log_file" ]]; then
            log_error "Launch log output:"
            cat "$log_file"
        else
            log_error "No launch log found at: $log_file"
        fi
        echo ""
        log_error "Try closing any existing VSCodium instances and run again"
        exit 1
    fi
}

# Main function
main() {
    log_info "🔒 Sandboxed VSCodium Fingerprint Runner"
    echo ""

    if [[ "$LIST_MODE" == "true" ]]; then
        list_fingerprints
        exit 0
    fi

    if [[ "$CLEAN_MODE" == "true" ]]; then
        cleanup
        exit 0
    fi

    # Validate sandbox configuration
    validate_sandbox_config

    # Display sandbox information
    if [[ "$SANDBOX_LEVEL" != "none" ]]; then
        local selected_sandbox
        selected_sandbox=$(select_sandbox_type "$SANDBOX_TYPE")
        local capabilities
        mapfile -t capabilities < <(detect_sandbox_capabilities)

        log_info "Sandbox Configuration:"
        log_info "  Level: $SANDBOX_LEVEL"
        log_info "  Type: $selected_sandbox (requested: $SANDBOX_TYPE)"
        log_info "  Network: $NETWORK_ISOLATION"
        log_info "  Resource Limits: $RESOURCE_LIMITS"
        log_info "  Available: ${capabilities[*]}"
        echo ""

        if [[ "$selected_sandbox" != "$SANDBOX_TYPE" && "$SANDBOX_TYPE" != "auto" ]]; then
            log_warning "Requested sandbox type '$SANDBOX_TYPE' not available, using '$selected_sandbox'"
        fi
    else
        log_warning "Sandbox disabled - using profile isolation only"
        echo ""
    fi

    check_vscodium
    
    local fp_id=""
    if [[ "$GENERATE_NEW" == "true" ]]; then
        # Capture fingerprint ID properly using a more robust method
        fp_id=$(generate_fingerprint 2>/dev/null)

        # Validate the fingerprint ID format and ensure it's not empty
        if [[ -z "$fp_id" ]] || [[ ! "$fp_id" =~ ^fp_[0-9]{8}_[0-9]{6}$ ]]; then
            log_error "Invalid or empty fingerprint ID generated: '$fp_id'"
            log_error "Falling back to timestamp-based ID"
            fp_id="fp_$(date +%Y%m%d_%H%M%S)"
        fi
    elif [[ -n "$FINGERPRINT_ID" ]]; then
        fp_id="$FINGERPRINT_ID"
        if [[ ! -f "$FINGERPRINTS_DIR/${fp_id}.json" ]]; then
            log_error "Fingerprint not found: $fp_id"
            list_fingerprints
            exit 1
        fi
    else
        log_error "No fingerprint specified. Use --new-fingerprint or --fingerprint-id"
        show_help
        exit 1
    fi

    log_info "Using fingerprint: $fp_id"

    # Create profile and capture directory path properly
    local profile_dir
    profile_dir=$(create_profile "$fp_id" 2>/dev/null)

    # Validate profile directory
    if [[ -z "$profile_dir" ]] || [[ ! -d "$profile_dir" ]]; then
        log_error "Failed to create profile directory: '$profile_dir'"
        log_error "Attempting to recreate profile with fallback method..."

        # Fallback: construct the profile directory path manually
        local profile_name="vscodium-fp-${fp_id}"
        profile_dir="$PROFILES_DIR/$profile_name"

        if [[ ! -d "$profile_dir" ]]; then
            log_error "Profile directory does not exist and fallback failed: $profile_dir"
            exit 1
        fi
    fi

    launch_vscodium "$profile_dir"
}

main "$@"
