{"id": "77c56d54-2347-4739-b50f-4b918af78d5a", "version": "1.0.0", "created_at": "2025-08-08T08:04:19.366364Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-09T08:04:19.366364Z", "device_id": "MXBKCgSc1BGAwWP1rig42g", "hardware_signature": {"cpu_signature": "apple-apple-silicon-213", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "hbeiZsF-5xm4B2MkyOYE7g", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "8dcSmhKMc3qNovtrytz6uA", "username_hash": "YO_s9gRirslzqSLi8tN_hQ"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "LcaWEweBHeFYORqCnFjEhg", "session_signature": "JON65qz1NaA", "workspace_signature": "dSjhPOiZ17M", "extensions_signature": "gVA622NlLB0"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-y-Q1n7Pj", "network_class": "ethernet", "connectivity_signature": "48wy_0pDG-A"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "5582cf038193c7c8f56b863ecd580be4db2acf9b78b8f7a74a3eaf199bd69838"}