#!/bin/bash
set -euo pipefail

# Apply resource limits if enabled
if [[ "true" == "true" ]]; then
    # Set comprehensive ulimits (macOS compatible)
    ulimit -c 0          # No core dumps
    ulimit -f 1048576    # Max file size 1GB
    ulimit -n 1024       # Max open files
    ulimit -u 512        # Max processes
    ulimit -s 8192       # Max stack size 8MB
    ulimit -t 3600       # Max CPU time 1 hour

    # Virtual memory limit (macOS doesn't support -v, use -d for data segment)
    if [[ "$(uname)" == "Darwin" ]]; then
        ulimit -d 2097152 2>/dev/null || true  # Max data segment 2GB (macOS)
    else
        ulimit -v 2097152 2>/dev/null || true  # Max virtual memory 2GB (Linux)
    fi

    # Set process priority
    renice 10 $$ 2>/dev/null || true
fi

# Set secure environment
export TMPDIR="${TMPDIR:-/tmp}/vscodium-$$"
mkdir -p "$TMPDIR"
chmod 700 "$TMPDIR"

# Clear potentially dangerous environment variables
unset SSH_AUTH_SOCK SSH_AGENT_PID GPG_AGENT_INFO

# Set secure umask
umask 077

# Launch VSCodium with sandbox
exec sandbox-exec -f "/Users/<USER>/Documents/augment-projects/exploit-extension/.vscodium-profiles/vscodium-fp-fp_20250808_150714/enhanced-sandbox.sb" "$@"
