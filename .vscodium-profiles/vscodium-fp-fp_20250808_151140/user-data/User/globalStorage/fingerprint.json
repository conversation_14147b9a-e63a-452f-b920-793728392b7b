{"id": "0fd9cb03-d62a-442b-a892-46a9d0aa951f", "version": "1.0.0", "created_at": "2025-08-08T08:11:41.635248Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-09T08:11:41.635248Z", "device_id": "MIlrzIKo_dMPZH49FcwmvA", "hardware_signature": {"cpu_signature": "apple-apple-silicon-92", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "idQqndrpaYxONMR-sawgXw", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "fMGPN092wUrrtNYN6rY6_Q", "username_hash": "8fvfz76EBvTmXsmoaEeU1g"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "6AI1FfayEiy5l1lP4L76zg", "session_signature": "fJVy8A014lw", "workspace_signature": "V9XX96a8aYE", "extensions_signature": "fT5ta67oxGg"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-xujQsTFp", "network_class": "ethernet", "connectivity_signature": "Vho513HxMkg"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "a55cae59621b45e76d5c340225fa942f3aa6f76320ed250d73951a0aa5c9c3fb"}