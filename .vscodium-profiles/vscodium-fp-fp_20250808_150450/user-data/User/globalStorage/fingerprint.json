{"id": "01c4a96e-f74a-48c3-b550-333673af5a33", "version": "1.0.0", "created_at": "2025-08-08T08:04:51.422061Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-09T08:04:51.422061Z", "device_id": "VZqUVgMyb0guhIYAoMds5Q", "hardware_signature": {"cpu_signature": "apple-apple-silicon-633", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "f9OFgo2pdNcfR-A55CCCpg", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "9_VBCryc0QfNHxGCgByKZw", "username_hash": "BdVKPvTx8kXR4pm8_Y10Wg"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "ko4ARtNpoShZ-OAihjatbw", "session_signature": "1lXTMx8paHc", "workspace_signature": "vrQMAlbboP4", "extensions_signature": "UWTii9MLzgA"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-xmtI7k87", "network_class": "ethernet", "connectivity_signature": "SXVmSCcG0vk"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "2f4a7e8361bb7a7328a7fb09a10fa03f422b21f877aa21ac3944b9b059f847c9"}