{"id": "0633cb27-cd15-43af-994e-98048692b772", "version": "1.0.0", "created_at": "2025-08-08T08:02:55.973796Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-09T08:02:55.973796Z", "device_id": "x4Ub9JulmD40hlz8rTBjJA", "hardware_signature": {"cpu_signature": "apple-apple-silicon-343", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "ZSdACA7_vrgGv3kcJWVYhQ", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "ZWn4aBU09L7hfRuEGib0Uw", "username_hash": "iTdQHgVtL78-6P-hVXL5mg"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "GXTmgw1aWKJIeIrkPzWL1g", "session_signature": "WBofV1s4lTo", "workspace_signature": "QT9_-2u3oAQ", "extensions_signature": "fAOtN9tcP3M"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-UCyoQQ7C", "network_class": "ethernet", "connectivity_signature": "_CjnPbfYLho"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "3d5ba964def336756909d2dfa47d1ca641016be80f665e0c9601d003d90e8f5a"}