{"id": "cb6189c9-ef32-4484-aaef-7e7c4ae2312e", "version": "1.0.0", "created_at": "2025-08-08T08:07:37.586976Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-09T08:07:37.586976Z", "device_id": "4Xl1biWhlH_1rVz4mG7smw", "hardware_signature": {"cpu_signature": "apple-apple-silicon-429", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "-4KnZdYLZRFe5sTICVUe7g", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "NLij3ol1-EF_90b6fehCxw", "username_hash": "e3CmkdZsUabChp3wOtj2cg"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "90P9ioqYpJyXC8yjF0lzog", "session_signature": "Xi8cf-EDrcQ", "workspace_signature": "ESH0D7h0FAg", "extensions_signature": "Lu7kbRqy_W0"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-LpMscbic", "network_class": "ethernet", "connectivity_signature": "mMzl4HzNzY0"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "6209e7d95daf8e955af79eb02e39190783948cce60b0bb9441483036b57d5cec"}