{"id": "2d2d455b-1eab-4655-a2b9-313aab98c3a2", "version": "1.0.0", "created_at": "2025-08-08T07:55:58.038789Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-09T07:55:58.038789Z", "device_id": "mbc51B58prZLIEEDzAte7A", "hardware_signature": {"cpu_signature": "apple-apple-silicon-459", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "rFz0mC9rFZ2Rue_TwhIauA", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "WBPRD10zdLBS0PH_JDUw0A", "username_hash": "3SSHpEBZwkRLJNGJ2pUdDQ"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "H3mzviKJDtmwFXjYCmVzAw", "session_signature": "-J9OD5Qb1Oc", "workspace_signature": "3mrkF9dYOcw", "extensions_signature": "tn5E9Zw9_Q8"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-uDVUtq8i", "network_class": "ethernet", "connectivity_signature": "KjuOkmFO19Y"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "31fbb39296a107347457fe645c15557b1d2ebc6fa4fd8f09c12877d75b690b12"}