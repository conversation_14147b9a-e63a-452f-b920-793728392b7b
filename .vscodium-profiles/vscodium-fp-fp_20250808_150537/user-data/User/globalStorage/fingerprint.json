{"id": "e85582df-3a38-46f9-b5e6-a9fd89ef38b8", "version": "1.0.0", "created_at": "2025-08-08T08:05:38.245323Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-09T08:05:38.245323Z", "device_id": "g3jqPAWscYhsFxqZ13M80Q", "hardware_signature": {"cpu_signature": "apple-apple-silicon-139", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "M2FN4cyYnPKBUjBxv2PJ0A", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "7zb6Wf2nZdN_WvaP-8hYiw", "username_hash": "FBW0HxBYhbC8Gcpa990d8g"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "ln-VfApLZRdMNdPtwwk_IQ", "session_signature": "k0kTD9eSIkw", "workspace_signature": "5-imLTP8NJI", "extensions_signature": "HLWaySkMeNo"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-rexE_imH", "network_class": "ethernet", "connectivity_signature": "djGROUgA0YU"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "b8a84c03d518bfdd8a405f59e75ec8ebbb73f3c64024f200ec424e1339bcc3b0"}