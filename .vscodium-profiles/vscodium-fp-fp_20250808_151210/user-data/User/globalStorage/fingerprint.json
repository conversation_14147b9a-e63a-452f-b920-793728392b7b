{"id": "9273ecde-00cf-459d-91fb-5fec05a3cdfd", "version": "1.0.0", "created_at": "2025-08-08T08:12:10.850611Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-09T08:12:10.850611Z", "device_id": "WEAcBxVMy2jUkaWwcIjwCw", "hardware_signature": {"cpu_signature": "apple-apple-silicon-756", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "fCWWMTJk_22lBNsCem4tqg", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "-EV5B7fSokH5npt-NMnxJw", "username_hash": "bf2kjoAr6fzogeey7S4XEQ"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "WCS7RgkAKBAEQ98u-tf5_Q", "session_signature": "50IzdywSdq8", "workspace_signature": "epbzUcdoIyw", "extensions_signature": "9DrchRp1oJU"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-qzpK6KWP", "network_class": "ethernet", "connectivity_signature": "30bBKXRjhwQ"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "4e9e65bb339942ebd5f0c1d8d684282ce3c43fe3660e188831e4cc3196db8c77"}