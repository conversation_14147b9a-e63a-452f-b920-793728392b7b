(version 1)
(deny default)
(import "system.sb")

; === BASIC SYSTEM OPERATIONS ===
(allow process-info* (target self))
(allow process-info-pidinfo (target self))
(allow process-info-setcontrol (target self))
(allow process-fork)
(allow process-exec
    (literal "/Applications/VSCodium.app/Contents/Resources/app/bin/codium")
    (subpath "/Applications/VSCodium.app")
    (subpath "/usr/bin")
    (subpath "/bin"))

; === SIGNAL HANDLING ===
(allow signal (target self))
(allow signal (target children))

; === MEMORY MANAGEMENT ===
(allow system-audit)
(allow mach-lookup
    (global-name "com.apple.system.logger")
    (global-name "com.apple.system.notification_center"))

; === FILE SYSTEM ACCESS ===
; Allow read access to system libraries and frameworks
(allow file-read*
    (subpath "/System/Library")
    (subpath "/usr/lib")
    (subpath "/usr/share")
    (subpath "/usr/bin")
    (subpath "/bin")
    (subpath "/Library/Frameworks")
    (subpath "/Library/Application Support")
    (literal "/etc/localtime")
    (literal "/etc/passwd")
    (literal "/etc/group")
    (literal "/var/db/timezone/localtime")
    (regex #"^/usr/share/zoneinfo/"))

; Allow execution of essential system utilities and VSCodium components
(allow process-exec
    (literal "/usr/bin/dirname")
    (literal "/bin/dirname")
    (literal "/usr/bin/basename")
    (literal "/bin/basename")
    (literal "/usr/bin/readlink")
    (literal "/bin/readlink")
    (literal "/usr/bin/which")
    (literal "/bin/which")
    (subpath "/Applications/VSCodium.app")
    (literal "/Applications/VSCodium.app/Contents/Resources/app/bin/codium"))

; Allow read/write access to application directories
(allow file-read* file-write*
    (subpath "/Users/<USER>/Documents/augment-projects/exploit-extension/.vscodium-profiles/vscodium-fp-fp_20250808_151210/user-data")
    (subpath "/Users/<USER>/Documents/augment-projects/exploit-extension/.vscodium-profiles/vscodium-fp-fp_20250808_151210/extensions")
    (subpath "/Users/<USER>/Documents/augment-projects/exploit-extension"))

; Allow temporary file access
(allow file-read* file-write*
    (subpath "/tmp")
    (subpath "/var/tmp")
    (regex #"^/private/tmp/")
    (regex #"^/private/var/tmp/"))

; Allow access to user home directory for Electron
(allow file-read*
    (subpath "/Users/<USER>/Library/Preferences")
    (subpath "/Users/<USER>/Library/Caches")
    (literal "/Users/<USER>/.CFUserTextEncoding"))

; Allow system configuration access
(allow file-read*
    (literal "/Library/Preferences/com.apple.security.plist")
    (subpath "/System/Library/CoreServices")
    (subpath "/usr/share/icu"))

; Allow access to essential device files
(allow file-read* file-write*
    (literal "/dev/null")
    (literal "/dev/zero")
    (literal "/dev/random")
    (literal "/dev/urandom")
    (literal "/dev/stdin")
    (literal "/dev/stdout")
    (literal "/dev/stderr"))

; === STRICT SECURITY RESTRICTIONS ===
; Allow broader system access for Electron compatibility (strict but functional)
(allow file-read*
    (subpath "/Applications")
    (subpath "/usr/local")
    (subpath "/opt"))

; Allow broader process execution for strict level (needed for Electron)
(allow process-exec
    (subpath "/Applications")
    (subpath "/usr/local/bin")
    (subpath "/usr/bin")
    (subpath "/bin"))

; Restrict some dangerous operations (strict level)
(deny file-write*
    (subpath "/System")
    (subpath "/usr")
    (subpath "/bin")
    (subpath "/sbin"))

; === FULL NETWORK ACCESS ===
(allow network*)
(allow system-socket)

; === GUI AND DISPLAY ACCESS ===
; Allow window server access for GUI
(allow mach-lookup
    (global-name "com.apple.windowserver.active")
    (global-name "com.apple.windowserver")
    (global-name "com.apple.CoreServices.coreservicesd")
    (global-name "com.apple.dock.server")
    (global-name "com.apple.pasteboard.1")
    (global-name "com.apple.distributed_notifications@1v3"))

; Allow IOKit access for input devices
(allow iokit-open
    (iokit-user-client-class "IOHIDParamUserClient")
    (iokit-user-client-class "IOHIDEventSystemUserClient")
    (iokit-user-client-class "IOAcceleratorES")
    (iokit-user-client-class "IOSurfaceRootUserClient"))

; Allow Core Graphics and display access
(allow iokit-get-properties)
(allow ipc-posix-shm-read-data
    (ipc-posix-name-regex #"^/tmp/com\.apple\.csseed\."))

; Allow font access
(allow file-read*
    (subpath "/System/Library/Fonts")
    (subpath "/Library/Fonts")
    (subpath "/System/Library/Assets/com_apple_MobileAsset_Font"))

; === RESOURCE LIMITS ===
; Note: macOS sandbox doesn't directly support resource limits
; These are handled by the wrapper script

; === FINAL SECURITY RULES ===
; Allow essential system services
(allow mach-lookup
    (global-name "com.apple.system.opendirectoryd.libinfo")
    (global-name "com.apple.system.logger"))

; Allow sysctl access for system information
(allow sysctl-read)

