{"id": "f778c15a-710a-405a-a709-18dfd95cebc3", "version": "1.0.0", "created_at": "2025-08-08T08:09:06.795237Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-09T08:09:06.795237Z", "device_id": "bs7UTRPN8bSIhEcPJICsug", "hardware_signature": {"cpu_signature": "apple-apple-silicon-45", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "sWl6mwLZMLL0ieql4uh8Ag", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "3cWW3cOrDpNG-Zlm18yPWg", "username_hash": "eOGO8tD39_SWWq2TQGr1qA"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "8AdacPKelZHPc5LS_P8dBQ", "session_signature": "YWHXeffvKkw", "workspace_signature": "UUNdtAK7g5w", "extensions_signature": "_0pfZmbMEtQ"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-UuDxLmxP", "network_class": "ethernet", "connectivity_signature": "--DtLECTcrs"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "77737ced99f39d006a4b28972ae5408b624dbdd5764a20b1d34a359be9d5b120"}