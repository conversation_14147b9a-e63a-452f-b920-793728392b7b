#!/bin/bash
# Simple VSCodium Launcher with basic isolation

# Set environment variables for better isolation
export VSCODE_PORTABLE="/Users/<USER>/Documents/augment-projects/exploit-extension/.vscodium-profiles/vscodium-fp-fp_20250808_150906/user-data"
export VSCODE_APPDATA="/Users/<USER>/Documents/augment-projects/exploit-extension/.vscodium-profiles/vscodium-fp-fp_20250808_150906/user-data"
export VSCODE_LOGS="/Users/<USER>/Documents/augment-projects/exploit-extension/.vscodium-profiles/vscodium-fp-fp_20250808_150906/user-data/logs"

# Apply basic resource limits if enabled
if [[ "false" == "true" ]]; then
    ulimit -v 2097152 2>/dev/null || ulimit -d 2097152 2>/dev/null || true  # 2GB memory limit
    ulimit -u 512 2>/dev/null || true      # Max 512 processes
    ulimit -f 1048576 2>/dev/null || true  # Max 1GB file size
fi

# Set restrictive umask for created files
umask 077

# Clear potentially sensitive environment variables
unset SSH_AUTH_SOCK SSH_AGENT_PID GPG_AGENT_INFO

# Set process name to distinguish from system VSCodium
exec -a "🔒 Sandboxed VSCodium (vscodium-fp-fp_20250808_150906)" "$@"
