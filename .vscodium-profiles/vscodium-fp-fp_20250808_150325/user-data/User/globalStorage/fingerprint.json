{"id": "7ba8401d-b097-4698-8764-34d64f0ee725", "version": "1.0.0", "created_at": "2025-08-08T08:03:26.521495Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-09T08:03:26.521495Z", "device_id": "Gzfwk3YRcBfazgJN81RtZQ", "hardware_signature": {"cpu_signature": "apple-apple-silicon-385", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "cyu6QiEicbeQWTguFMWIag", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "Ko3-rGBmQmwSzt7ly9xSFw", "username_hash": "6aubx0q4sknubsFfH5kvaw"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "zzSWkWR9ISz7YMGY6Sjacw", "session_signature": "024650oi0A0", "workspace_signature": "LMJWmcYTOE8", "extensions_signature": "iMkmfMqSfKs"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-wJFuqFGg", "network_class": "ethernet", "connectivity_signature": "Qg2-ctPsG6g"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "3283b0ed492a80a4ab5837adc91b6d8de4350ebbdfc46bd8aca5a255506b3bab"}