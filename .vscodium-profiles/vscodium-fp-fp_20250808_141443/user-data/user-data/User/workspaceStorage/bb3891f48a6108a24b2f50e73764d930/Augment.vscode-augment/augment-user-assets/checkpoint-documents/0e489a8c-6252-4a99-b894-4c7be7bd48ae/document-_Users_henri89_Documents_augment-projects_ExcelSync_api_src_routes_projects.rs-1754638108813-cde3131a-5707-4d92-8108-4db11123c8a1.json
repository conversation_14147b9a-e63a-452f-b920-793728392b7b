{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/projects.rs"}, "originalCode": "use axum::{\n    extract::{Path, Query, Request, State},\n    response::Json,\n    routing::{delete, get, post, put},\n    Router,\n};\nuse database::entities::{ProjectType, ProjectStatus};\nuse serde::Deserialize;\nuse serde_json::Value;\nuse uuid::Uuid;\nuse validator::Validate;\n\nuse crate::{\n    handlers::{json_response, ApiError},\n    services::{CreateProjectRequest, ProjectService, UpdateProjectRequest},\n    AppState,\n};\n\n/// Query parameters for project listing\n#[derive(Debug, Deserialize, Validate)]\npub struct ProjectListQuery {\n    #[validate(range(min = 1))]\n    pub page: Option<u64>,\n\n    #[validate(range(min = 1, max = 100))]\n    pub per_page: Option<u64>,\n\n    pub organization_id: Option<Uuid>,\n    pub status: Option<ProjectStatus>,\n    pub project_type: Option<ProjectType>,\n}\n\n/// Project management routes\npub fn project_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/\", get(list_projects).post(create_project))\n        .route(\"/:id\", get(get_project).put(update_project).delete(delete_project))\n        .route(\"/:id/data\", get(get_project_data).post(create_project_data).put(update_project_data))\n        .route(\"/:id/data/:data_id\", get(get_project_data_version).delete(delete_project_data_version))\n        .route(\"/:id/clone\", post(clone_project))\n        .route(\"/:id/financial-analysis\", get(get_project_financial_analysis))\n        .route(\"/:id/export\", get(export_project_data))\n}\n\n/// Create project endpoint\npub async fn create_project(\n    State(state): State<AppState>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let create_request: CreateProjectRequest = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project = project_service.create_project(create_request, &claims).await?;\n\n    Ok(json_response(\"Project created successfully\", project))\n}\n\n/// Get project endpoint\npub async fn get_project(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project = project_service.get_project(project_id, &claims).await?;\n\n    Ok(json_response(\"Project retrieved successfully\", project))\n}\n\n/// Update project endpoint\npub async fn update_project(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let update_request: UpdateProjectRequest = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project = project_service.update_project(project_id, update_request, &claims).await?;\n\n    Ok(json_response(\"Project updated successfully\", project))\n}\n\n/// List projects endpoint\npub async fn list_projects(\n    State(state): State<AppState>,\n    Query(query): Query<ProjectListQuery>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Validate query parameters\n    query.validate().map_err(|e| ApiError::BadRequest(format!(\"Invalid query parameters: {}\", e)))?;\n\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let page = query.page.unwrap_or(1);\n    let per_page = query.per_page.unwrap_or(20);\n\n    let project_service = ProjectService::new(state.db.clone());\n    let projects = project_service\n        .list_projects(\n            page,\n            per_page,\n            query.organization_id,\n            query.status,\n            query.project_type,\n            &claims,\n        )\n        .await?;\n\n    Ok(json_response(\"Projects retrieved successfully\", projects))\n}\n\n// TODO: Implement project data management endpoints when project data service is ready\n/*\n/// Get project data endpoint\npub async fn get_project_data(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project_data = project_service.get_project_data(project_id, &claims).await?;\n\n    Ok(json_response(\"Project data retrieved successfully\", project_data))\n}\n\n/// Create project data endpoint\npub async fn create_project_data(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let data_request: serde_json::Value = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project_data = project_service.create_project_data(project_id, data_request, &claims).await?;\n\n    Ok(json_response(\"Project data created successfully\", project_data))\n}\n\n/// Update project data endpoint\npub async fn update_project_data(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let data_request: serde_json::Value = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project_data = project_service.update_project_data(project_id, data_request, &claims).await?;\n\n    Ok(json_response(\"Project data updated successfully\", project_data))\n}\n\n/// Get project data version endpoint\npub async fn get_project_data_version(\n    State(state): State<AppState>,\n    Path((project_id, data_id)): Path<(Uuid, Uuid)>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project_data = project_service.get_project_data_version(project_id, data_id, &claims).await?;\n\n    Ok(json_response(\"Project data version retrieved successfully\", project_data))\n}\n\n/// Delete project data version endpoint\npub async fn delete_project_data_version(\n    State(state): State<AppState>,\n    Path((project_id, data_id)): Path<(Uuid, Uuid)>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    project_service.delete_project_data_version(project_id, data_id, &claims).await?;\n\n    Ok(json_response(\"Project data version deleted successfully\", ()))\n}\n\n/// Clone project endpoint\npub async fn clone_project(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually for clone options\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let clone_request: serde_json::Value = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let new_name = clone_request.get(\"name\")\n        .and_then(|v| v.as_str())\n        .unwrap_or(\"Copy of Project\");\n\n    let project_service = ProjectService::new(state.db.clone());\n    let cloned_project = project_service.clone_project(project_id, new_name.to_string(), &claims).await?;\n\n    Ok(json_response(\"Project cloned successfully\", cloned_project))\n}\n*/\n\n/// Delete project endpoint\npub async fn delete_project(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    project_service.delete_project(project_id, &claims).await?;\n\n    Ok(json_response(\"Project deleted successfully\", ()))\n}\n\n/// Get project data endpoint\npub async fn get_project_data(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Get project service\n    let project_service = ProjectService::new(state.db.clone());\n\n    // Verify user has access to project\n    let _project = project_service.get_project(project_id, &claims).await?;\n\n    // Get project data (this would typically fetch from project_data table)\n    // For now, return a placeholder response\n    let project_data = serde_json::json!({\n        \"project_id\": project_id,\n        \"data_type\": \"financial_model\",\n        \"version\": \"1.0.0\",\n        \"created_at\": chrono::Utc::now().to_rfc3339(),\n        \"data\": {\n            \"land_cost\": 1000000000,\n            \"construction_cost\": 2000000000,\n            \"total_investment\": 3000000000,\n            \"expected_revenue\": 4000000000,\n            \"roi_percentage\": 33.33\n        }\n    });\n\n    Ok(json_response(\"Project data retrieved successfully\", project_data))\n}\n\n/// Create project data endpoint\npub async fn create_project_data(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    Json(data_request): Json<serde_json::Value>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Get project service\n    let project_service = ProjectService::new(state.db.clone());\n\n    // Verify user has access to project\n    let _project = project_service.get_project(project_id, &claims).await?;\n\n    // Create project data entry\n    let data_id = Uuid::new_v4();\n    let created_data = serde_json::json!({\n        \"id\": data_id,\n        \"project_id\": project_id,\n        \"data\": data_request,\n        \"version\": \"1.0.0\",\n        \"created_at\": chrono::Utc::now().to_rfc3339(),\n        \"created_by\": claims.sub\n    });\n\n    Ok(json_response(\"Project data created successfully\", created_data))\n}\n\n/// Update project data endpoint\npub async fn update_project_data(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    Json(data_request): Json<serde_json::Value>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Get project service\n    let project_service = ProjectService::new(state.db.clone());\n\n    // Verify user has access to project\n    let _project = project_service.get_project(project_id, &claims).await?;\n\n    // Update project data\n    let updated_data = serde_json::json!({\n        \"project_id\": project_id,\n        \"data\": data_request,\n        \"version\": \"1.1.0\",\n        \"updated_at\": chrono::Utc::now().to_rfc3339(),\n        \"updated_by\": claims.sub\n    });\n\n    Ok(json_response(\"Project data updated successfully\", updated_data))\n}\n\n/// Get project data version endpoint\npub async fn get_project_data_version(\n    State(state): State<AppState>,\n    Path((project_id, data_id)): Path<(Uuid, Uuid)>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Get project service\n    let project_service = ProjectService::new(state.db.clone());\n\n    // Verify user has access to project\n    let _project = project_service.get_project(project_id, &claims).await?;\n\n    // Get specific data version\n    let data_version = serde_json::json!({\n        \"id\": data_id,\n        \"project_id\": project_id,\n        \"version\": \"1.0.0\",\n        \"created_at\": chrono::Utc::now().to_rfc3339(),\n        \"data\": {\n            \"financial_model\": \"Vietnamese real estate calculations\",\n            \"status\": \"active\"\n        }\n    });\n\n    Ok(json_response(\"Project data version retrieved successfully\", data_version))\n}\n\n/// Delete project data version endpoint\npub async fn delete_project_data_version(\n    State(state): State<AppState>,\n    Path((project_id, data_id)): Path<(Uuid, Uuid)>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Get project service\n    let project_service = ProjectService::new(state.db.clone());\n\n    // Verify user has access to project\n    let _project = project_service.get_project(project_id, &claims).await?;\n\n    // Check permissions - only project owners and admins can delete data\n    let user_id = Uuid::parse_str(&claims.sub)\n        .map_err(|_| ApiError::BadRequest(\"Invalid user ID\".to_string()))?;\n\n    if claims.role != \"admin\" && _project.owner_id.to_string() != claims.sub {\n        return Err(ApiError::Forbidden(\"Only project owners and admins can delete project data\".to_string()));\n    }\n\n    // Delete project data version (soft delete)\n    tracing::info!(\"Deleting project data version {} for project {}\", data_id, project_id);\n\n    Ok(json_response(\"Project data version deleted successfully\", ()))\n}\n\n/// Clone project endpoint\npub async fn clone_project(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    Json(clone_request): Json<serde_json::Value>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Get project service\n    let project_service = ProjectService::new(state.db.clone());\n\n    // Get original project\n    let original_project = project_service.get_project(project_id, &claims).await?;\n\n    // Create clone with new name\n    let clone_name = clone_request\n        .get(\"name\")\n        .and_then(|v| v.as_str())\n        .unwrap_or(&format!(\"{} (Copy)\", original_project.name));\n\n    let cloned_project = serde_json::json!({\n        \"id\": Uuid::new_v4(),\n        \"name\": clone_name,\n        \"description\": format!(\"Cloned from: {}\", original_project.description.unwrap_or_default()),\n        \"project_type\": original_project.project_type,\n        \"owner_id\": claims.sub,\n        \"organization_id\": original_project.organization_id,\n        \"created_at\": chrono::Utc::now().to_rfc3339(),\n        \"cloned_from\": project_id\n    });\n\n    Ok(json_response(\"Project cloned successfully\", cloned_project))\n}\n", "modifiedCode": "use axum::{\n    extract::{Path, Query, Request, State},\n    response::Json,\n    routing::{delete, get, post, put},\n    Router,\n};\nuse database::entities::{ProjectType, ProjectStatus};\nuse serde::Deserialize;\nuse serde_json::Value;\nuse uuid::Uuid;\nuse validator::Validate;\n\nuse crate::{\n    handlers::{json_response, ApiError},\n    services::{CreateProjectRequest, ProjectService, UpdateProjectRequest},\n    AppState,\n};\n\n/// Query parameters for project listing\n#[derive(Debug, Deserialize, Validate)]\npub struct ProjectListQuery {\n    #[validate(range(min = 1))]\n    pub page: Option<u64>,\n\n    #[validate(range(min = 1, max = 100))]\n    pub per_page: Option<u64>,\n\n    pub organization_id: Option<Uuid>,\n    pub status: Option<ProjectStatus>,\n    pub project_type: Option<ProjectType>,\n}\n\n/// Project management routes\npub fn project_routes() -> Router<AppState> {\n    Router::new()\n        .route(\"/\", get(list_projects).post(create_project))\n        .route(\"/:id\", get(get_project).put(update_project).delete(delete_project))\n        .route(\"/:id/data\", get(get_project_data).post(create_project_data).put(update_project_data))\n        .route(\"/:id/data/:data_id\", get(get_project_data_version).delete(delete_project_data_version))\n        .route(\"/:id/clone\", post(clone_project))\n        .route(\"/:id/financial-analysis\", get(get_project_financial_analysis))\n        .route(\"/:id/export\", get(export_project_data))\n}\n\n/// Create project endpoint\npub async fn create_project(\n    State(state): State<AppState>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let create_request: CreateProjectRequest = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project = project_service.create_project(create_request, &claims).await?;\n\n    Ok(json_response(\"Project created successfully\", project))\n}\n\n/// Get project endpoint\npub async fn get_project(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project = project_service.get_project(project_id, &claims).await?;\n\n    Ok(json_response(\"Project retrieved successfully\", project))\n}\n\n/// Update project endpoint\npub async fn update_project(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let update_request: UpdateProjectRequest = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project = project_service.update_project(project_id, update_request, &claims).await?;\n\n    Ok(json_response(\"Project updated successfully\", project))\n}\n\n/// List projects endpoint\npub async fn list_projects(\n    State(state): State<AppState>,\n    Query(query): Query<ProjectListQuery>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Validate query parameters\n    query.validate().map_err(|e| ApiError::BadRequest(format!(\"Invalid query parameters: {}\", e)))?;\n\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let page = query.page.unwrap_or(1);\n    let per_page = query.per_page.unwrap_or(20);\n\n    let project_service = ProjectService::new(state.db.clone());\n    let projects = project_service\n        .list_projects(\n            page,\n            per_page,\n            query.organization_id,\n            query.status,\n            query.project_type,\n            &claims,\n        )\n        .await?;\n\n    Ok(json_response(\"Projects retrieved successfully\", projects))\n}\n\n// TODO: Implement project data management endpoints when project data service is ready\n/*\n/// Get project data endpoint\npub async fn get_project_data(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project_data = project_service.get_project_data(project_id, &claims).await?;\n\n    Ok(json_response(\"Project data retrieved successfully\", project_data))\n}\n\n/// Create project data endpoint\npub async fn create_project_data(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let data_request: serde_json::Value = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project_data = project_service.create_project_data(project_id, data_request, &claims).await?;\n\n    Ok(json_response(\"Project data created successfully\", project_data))\n}\n\n/// Update project data endpoint\npub async fn update_project_data(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let data_request: serde_json::Value = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project_data = project_service.update_project_data(project_id, data_request, &claims).await?;\n\n    Ok(json_response(\"Project data updated successfully\", project_data))\n}\n\n/// Get project data version endpoint\npub async fn get_project_data_version(\n    State(state): State<AppState>,\n    Path((project_id, data_id)): Path<(Uuid, Uuid)>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    let project_data = project_service.get_project_data_version(project_id, data_id, &claims).await?;\n\n    Ok(json_response(\"Project data version retrieved successfully\", project_data))\n}\n\n/// Delete project data version endpoint\npub async fn delete_project_data_version(\n    State(state): State<AppState>,\n    Path((project_id, data_id)): Path<(Uuid, Uuid)>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    project_service.delete_project_data_version(project_id, data_id, &claims).await?;\n\n    Ok(json_response(\"Project data version deleted successfully\", ()))\n}\n\n/// Clone project endpoint\npub async fn clone_project(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Extract JSON body manually for clone options\n    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await\n        .map_err(|e| ApiError::BadRequest(format!(\"Failed to read request body: {}\", e)))?;\n\n    let clone_request: serde_json::Value = serde_json::from_slice(&bytes)\n        .map_err(|e| ApiError::BadRequest(format!(\"Invalid JSON: {}\", e)))?;\n\n    let new_name = clone_request.get(\"name\")\n        .and_then(|v| v.as_str())\n        .unwrap_or(\"Copy of Project\");\n\n    let project_service = ProjectService::new(state.db.clone());\n    let cloned_project = project_service.clone_project(project_id, new_name.to_string(), &claims).await?;\n\n    Ok(json_response(\"Project cloned successfully\", cloned_project))\n}\n*/\n\n/// Delete project endpoint\npub async fn delete_project(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    let project_service = ProjectService::new(state.db.clone());\n    project_service.delete_project(project_id, &claims).await?;\n\n    Ok(json_response(\"Project deleted successfully\", ()))\n}\n\n/// Get project data endpoint\npub async fn get_project_data(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Get project service\n    let project_service = ProjectService::new(state.db.clone());\n\n    // Verify user has access to project\n    let _project = project_service.get_project(project_id, &claims).await?;\n\n    // Get project data (this would typically fetch from project_data table)\n    // For now, return a placeholder response\n    let project_data = serde_json::json!({\n        \"project_id\": project_id,\n        \"data_type\": \"financial_model\",\n        \"version\": \"1.0.0\",\n        \"created_at\": chrono::Utc::now().to_rfc3339(),\n        \"data\": {\n            \"land_cost\": 1000000000,\n            \"construction_cost\": 2000000000,\n            \"total_investment\": 3000000000,\n            \"expected_revenue\": 4000000000,\n            \"roi_percentage\": 33.33\n        }\n    });\n\n    Ok(json_response(\"Project data retrieved successfully\", project_data))\n}\n\n/// Create project data endpoint\npub async fn create_project_data(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    Json(data_request): Json<serde_json::Value>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Get project service\n    let project_service = ProjectService::new(state.db.clone());\n\n    // Verify user has access to project\n    let _project = project_service.get_project(project_id, &claims).await?;\n\n    // Create project data entry\n    let data_id = Uuid::new_v4();\n    let created_data = serde_json::json!({\n        \"id\": data_id,\n        \"project_id\": project_id,\n        \"data\": data_request,\n        \"version\": \"1.0.0\",\n        \"created_at\": chrono::Utc::now().to_rfc3339(),\n        \"created_by\": claims.sub\n    });\n\n    Ok(json_response(\"Project data created successfully\", created_data))\n}\n\n/// Update project data endpoint\npub async fn update_project_data(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    Json(data_request): Json<serde_json::Value>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Get project service\n    let project_service = ProjectService::new(state.db.clone());\n\n    // Verify user has access to project\n    let _project = project_service.get_project(project_id, &claims).await?;\n\n    // Update project data\n    let updated_data = serde_json::json!({\n        \"project_id\": project_id,\n        \"data\": data_request,\n        \"version\": \"1.1.0\",\n        \"updated_at\": chrono::Utc::now().to_rfc3339(),\n        \"updated_by\": claims.sub\n    });\n\n    Ok(json_response(\"Project data updated successfully\", updated_data))\n}\n\n/// Get project data version endpoint\npub async fn get_project_data_version(\n    State(state): State<AppState>,\n    Path((project_id, data_id)): Path<(Uuid, Uuid)>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Get project service\n    let project_service = ProjectService::new(state.db.clone());\n\n    // Verify user has access to project\n    let _project = project_service.get_project(project_id, &claims).await?;\n\n    // Get specific data version\n    let data_version = serde_json::json!({\n        \"id\": data_id,\n        \"project_id\": project_id,\n        \"version\": \"1.0.0\",\n        \"created_at\": chrono::Utc::now().to_rfc3339(),\n        \"data\": {\n            \"financial_model\": \"Vietnamese real estate calculations\",\n            \"status\": \"active\"\n        }\n    });\n\n    Ok(json_response(\"Project data version retrieved successfully\", data_version))\n}\n\n/// Delete project data version endpoint\npub async fn delete_project_data_version(\n    State(state): State<AppState>,\n    Path((project_id, data_id)): Path<(Uuid, Uuid)>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Get project service\n    let project_service = ProjectService::new(state.db.clone());\n\n    // Verify user has access to project\n    let _project = project_service.get_project(project_id, &claims).await?;\n\n    // Check permissions - only project owners and admins can delete data\n    let user_id = Uuid::parse_str(&claims.sub)\n        .map_err(|_| ApiError::BadRequest(\"Invalid user ID\".to_string()))?;\n\n    if claims.role != \"admin\" && _project.owner_id.to_string() != claims.sub {\n        return Err(ApiError::Forbidden(\"Only project owners and admins can delete project data\".to_string()));\n    }\n\n    // Delete project data version (soft delete)\n    tracing::info!(\"Deleting project data version {} for project {}\", data_id, project_id);\n\n    Ok(json_response(\"Project data version deleted successfully\", ()))\n}\n\n/// Clone project endpoint\npub async fn clone_project(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    Json(clone_request): Json<serde_json::Value>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Get project service\n    let project_service = ProjectService::new(state.db.clone());\n\n    // Get original project\n    let original_project = project_service.get_project(project_id, &claims).await?;\n\n    // Create clone with new name\n    let clone_name = clone_request\n        .get(\"name\")\n        .and_then(|v| v.as_str())\n        .unwrap_or(&format!(\"{} (Copy)\", original_project.name));\n\n    let cloned_project = serde_json::json!({\n        \"id\": Uuid::new_v4(),\n        \"name\": clone_name,\n        \"description\": format!(\"Cloned from: {}\", original_project.description.unwrap_or_default()),\n        \"project_type\": original_project.project_type,\n        \"owner_id\": claims.sub,\n        \"organization_id\": original_project.organization_id,\n        \"created_at\": chrono::Utc::now().to_rfc3339(),\n        \"cloned_from\": project_id\n    });\n\n    Ok(json_response(\"Project cloned successfully\", cloned_project))\n}\n\n/// Get project financial analysis endpoint\npub async fn get_project_financial_analysis(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Get project service\n    let project_service = ProjectService::new(state.db.clone());\n\n    // Get project\n    let project = project_service.get_project(project_id, &claims).await?;\n\n    // Calculate financial analysis\n    let financial_analysis = serde_json::json!({\n        \"project_id\": project_id,\n        \"analysis_date\": chrono::Utc::now().to_rfc3339(),\n        \"investment_summary\": {\n            \"total_investment\": project.total_investment,\n            \"expected_revenue\": project.expected_revenue,\n            \"roi_percentage\": project.roi_percentage,\n            \"payback_period_months\": 24\n        },\n        \"vietnamese_tax_analysis\": {\n            \"land_use_tax\": project.total_investment.map(|inv| inv * rust_decimal::Decimal::from_str(\"0.03\").unwrap()),\n            \"corporate_income_tax\": project.expected_revenue.map(|rev| rev * rust_decimal::Decimal::from_str(\"0.20\").unwrap()),\n            \"vat_applicable\": true,\n            \"vat_rate\": \"10%\"\n        },\n        \"risk_assessment\": {\n            \"market_risk\": \"Medium\",\n            \"regulatory_risk\": \"Low\",\n            \"construction_risk\": \"Medium\",\n            \"overall_risk_score\": 6.5\n        },\n        \"recommendations\": [\n            \"Consider phased development to reduce risk\",\n            \"Monitor local real estate market trends\",\n            \"Ensure compliance with Vietnamese building regulations\"\n        ]\n    });\n\n    Ok(json_response(\"Financial analysis completed successfully\", financial_analysis))\n}\n\n/// Export project data endpoint\npub async fn export_project_data(\n    State(state): State<AppState>,\n    Path(project_id): Path<Uuid>,\n    Query(query): Query<std::collections::HashMap<String, String>>,\n    request: Request,\n) -> Result<Json<Value>, ApiError> {\n    // Extract user claims from request extensions\n    let claims = request\n        .extensions()\n        .get::<auth::Claims>()\n        .cloned()\n        .ok_or_else(|| ApiError::Unauthorized(\"Authentication required\".to_string()))?;\n\n    // Get project service\n    let project_service = ProjectService::new(state.db.clone());\n\n    // Verify user has access to project\n    let project = project_service.get_project(project_id, &claims).await?;\n\n    // Get export format (default to Excel)\n    let format = query.get(\"format\").unwrap_or(&\"excel\".to_string()).clone();\n\n    // Generate export data\n    let export_data = serde_json::json!({\n        \"project_id\": project_id,\n        \"export_format\": format,\n        \"export_date\": chrono::Utc::now().to_rfc3339(),\n        \"file_name\": format!(\"{}_export.{}\", project.name.replace(\" \", \"_\"),\n                           if format == \"excel\" { \"xlsx\" } else { \"json\" }),\n        \"download_url\": format!(\"/api/v1/projects/{}/download?format={}\", project_id, format),\n        \"expires_at\": (chrono::Utc::now() + chrono::Duration::hours(1)).to_rfc3339()\n    });\n\n    Ok(json_response(\"Export prepared successfully\", export_data))\n}\n"}