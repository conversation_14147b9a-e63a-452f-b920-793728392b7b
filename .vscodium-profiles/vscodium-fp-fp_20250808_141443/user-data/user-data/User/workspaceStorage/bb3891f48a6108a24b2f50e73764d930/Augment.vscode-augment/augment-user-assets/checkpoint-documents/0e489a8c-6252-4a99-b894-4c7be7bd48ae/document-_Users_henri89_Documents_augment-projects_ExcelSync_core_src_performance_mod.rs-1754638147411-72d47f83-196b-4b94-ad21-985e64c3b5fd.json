{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "core/src/performance/mod.rs"}, "originalCode": "pub mod response_optimizer;\npub mod caching;\npub mod memory_manager;\npub mod scaling;\n\npub use response_optimizer::*;\npub use caching::*;\npub use memory_manager::*;\npub use scaling::*;\n\nuse std::time::{Duration, Instant};\nuse std::sync::OnceLock;\nuse tracing::{info, warn};\n\n/// Global performance metrics instance\nstatic PERFORMANCE_METRICS: OnceLock<PerformanceMonitor> = OnceLock::new();\n\n/// Performance configuration for the application\n#[derive(Debug, Clone)]\npub struct PerformanceConfig {\n    /// Target response time for 95th percentile (milliseconds)\n    pub target_p95_response_time_ms: u64,\n    /// Target response time for 99th percentile (milliseconds)\n    pub target_p99_response_time_ms: u64,\n    /// Maximum allowed memory usage per instance (MB)\n    pub max_memory_usage_mb: u64,\n    /// Cache hit ratio target (0.0 to 1.0)\n    pub cache_hit_ratio_target: f64,\n    /// Enable performance monitoring\n    pub enable_monitoring: bool,\n}\n\nimpl Default for PerformanceConfig {\n    fn default() -> Self {\n        Self {\n            target_p95_response_time_ms: 200,\n            target_p99_response_time_ms: 500,\n            max_memory_usage_mb: 512,\n            cache_hit_ratio_target: 0.85,\n            enable_monitoring: true,\n        }\n    }\n}\n\n/// Performance metrics collector\n#[derive(Debug, Clone)]\npub struct PerformanceMetrics {\n    pub response_times: Vec<Duration>,\n    pub memory_usage_mb: f64,\n    pub cache_hit_ratio: f64,\n    pub active_connections: u32,\n    pub requests_per_second: f64,\n}\n\nimpl PerformanceMetrics {\n    pub fn new() -> Self {\n        Self {\n            response_times: Vec::new(),\n            memory_usage_mb: 0.0,\n            cache_hit_ratio: 0.0,\n            active_connections: 0,\n            requests_per_second: 0.0,\n        }\n    }\n\n    /// Calculate 95th percentile response time\n    pub fn p95_response_time(&self) -> Duration {\n        if self.response_times.is_empty() {\n            return Duration::from_millis(0);\n        }\n\n        let mut sorted_times = self.response_times.clone();\n        sorted_times.sort();\n        let index = (sorted_times.len() as f64 * 0.95) as usize;\n        sorted_times.get(index).copied().unwrap_or(Duration::from_millis(0))\n    }\n\n    /// Calculate 99th percentile response time\n    pub fn p99_response_time(&self) -> Duration {\n        if self.response_times.is_empty() {\n            return Duration::from_millis(0);\n        }\n\n        let mut sorted_times = self.response_times.clone();\n        sorted_times.sort();\n        let index = (sorted_times.len() as f64 * 0.99) as usize;\n        sorted_times.get(index).copied().unwrap_or(Duration::from_millis(0))\n    }\n\n    /// Calculate average response time\n    pub fn average_response_time(&self) -> Duration {\n        if self.response_times.is_empty() {\n            return Duration::from_millis(0);\n        }\n\n        let total: Duration = self.response_times.iter().sum();\n        total / self.response_times.len() as u32\n    }\n\n    /// Add a response time measurement\n    pub fn record_response_time(&mut self, duration: Duration) {\n        self.response_times.push(duration);\n        \n        // Keep only the last 1000 measurements to prevent memory growth\n        if self.response_times.len() > 1000 {\n            self.response_times.remove(0);\n        }\n    }\n\n    /// Check if performance targets are met\n    pub fn meets_targets(&self, config: &PerformanceConfig) -> bool {\n        let p95_ms = self.p95_response_time().as_millis() as u64;\n        let p99_ms = self.p99_response_time().as_millis() as u64;\n        \n        p95_ms <= config.target_p95_response_time_ms\n            && p99_ms <= config.target_p99_response_time_ms\n            && self.memory_usage_mb <= config.max_memory_usage_mb as f64\n            && self.cache_hit_ratio >= config.cache_hit_ratio_target\n    }\n}\n\nimpl Default for PerformanceMetrics {\n    fn default() -> Self {\n        Self::new()\n    }\n}\n\n/// Performance monitor that tracks and reports performance metrics\npub struct PerformanceMonitor {\n    config: PerformanceConfig,\n    metrics: PerformanceMetrics,\n    last_report: Instant,\n}\n\nimpl PerformanceMonitor {\n    pub fn new(config: PerformanceConfig) -> Self {\n        Self {\n            config,\n            metrics: PerformanceMetrics::new(),\n            last_report: Instant::now(),\n        }\n    }\n\n    /// Record a request completion\n    pub fn record_request(&mut self, duration: Duration) {\n        self.metrics.record_response_time(duration);\n        \n        // Check if we should report performance\n        if self.last_report.elapsed() >= Duration::from_secs(60) {\n            self.report_performance();\n            self.last_report = Instant::now();\n        }\n    }\n\n    /// Update memory usage\n    pub fn update_memory_usage(&mut self, usage_mb: f64) {\n        self.metrics.memory_usage_mb = usage_mb;\n    }\n\n    /// Update cache hit ratio\n    pub fn update_cache_hit_ratio(&mut self, ratio: f64) {\n        self.metrics.cache_hit_ratio = ratio;\n    }\n\n    /// Update active connections\n    pub fn update_active_connections(&mut self, count: u32) {\n        self.metrics.active_connections = count;\n    }\n\n    /// Update requests per second\n    pub fn update_requests_per_second(&mut self, rps: f64) {\n        self.metrics.requests_per_second = rps;\n    }\n\n    /// Report current performance metrics\n    pub fn report_performance(&self) {\n        if !self.config.enable_monitoring {\n            return;\n        }\n\n        let p95_ms = self.metrics.p95_response_time().as_millis();\n        let p99_ms = self.metrics.p99_response_time().as_millis();\n        let avg_ms = self.metrics.average_response_time().as_millis();\n\n        info!(\n            \"Performance Report - P95: {}ms, P99: {}ms, Avg: {}ms, Memory: {:.1}MB, Cache Hit: {:.2}%, RPS: {:.1}\",\n            p95_ms,\n            p99_ms,\n            avg_ms,\n            self.metrics.memory_usage_mb,\n            self.metrics.cache_hit_ratio * 100.0,\n            self.metrics.requests_per_second\n        );\n\n        // Check if targets are met\n        if !self.metrics.meets_targets(&self.config) {\n            warn!(\"Performance targets not met!\");\n            \n            if p95_ms > self.config.target_p95_response_time_ms as u128 {\n                warn!(\"P95 response time ({} ms) exceeds target ({} ms)\", \n                      p95_ms, self.config.target_p95_response_time_ms);\n            }\n            \n            if p99_ms > self.config.target_p99_response_time_ms as u128 {\n                warn!(\"P99 response time ({} ms) exceeds target ({} ms)\", \n                      p99_ms, self.config.target_p99_response_time_ms);\n            }\n            \n            if self.metrics.memory_usage_mb > self.config.max_memory_usage_mb as f64 {\n                warn!(\"Memory usage ({:.1} MB) exceeds target ({} MB)\", \n                      self.metrics.memory_usage_mb, self.config.max_memory_usage_mb);\n            }\n            \n            if self.metrics.cache_hit_ratio < self.config.cache_hit_ratio_target {\n                warn!(\"Cache hit ratio ({:.2}%) below target ({:.2}%)\", \n                      self.metrics.cache_hit_ratio * 100.0, \n                      self.config.cache_hit_ratio_target * 100.0);\n            }\n        }\n    }\n\n    /// Get current metrics\n    pub fn get_metrics(&self) -> &PerformanceMetrics {\n        &self.metrics\n    }\n\n    /// Get performance configuration\n    pub fn get_config(&self) -> &PerformanceConfig {\n        &self.config\n    }\n}\n\n/// Middleware for measuring request performance\npub async fn performance_middleware<B>(\n    req: axum::extract::Request<B>,\n    next: axum::middleware::Next<B>,\n) -> axum::response::Response {\n    let start = Instant::now();\n    let method = req.method().clone();\n    let uri = req.uri().clone();\n    \n    let response = next.run(req).await;\n    \n    let duration = start.elapsed();\n    let status = response.status();\n    \n    // Log slow requests\n    if duration > Duration::from_millis(200) {\n        warn!(\n            \"Slow request: {} {} - {}ms (status: {})\",\n            method,\n            uri,\n            duration.as_millis(),\n            status\n        );\n    }\n    \n    // Record metrics in performance monitor\n    PERFORMANCE_METRICS.record_request(\n        method.as_str(),\n        uri.path(),\n        status.as_u16(),\n        duration,\n    );\n    \n    response\n}\n\n/// Performance optimization recommendations\npub struct PerformanceRecommendations;\n\nimpl PerformanceRecommendations {\n    /// Generate performance optimization recommendations\n    pub fn generate(metrics: &PerformanceMetrics, config: &PerformanceConfig) -> Vec<String> {\n        let mut recommendations = Vec::new();\n        \n        let p95_ms = metrics.p95_response_time().as_millis() as u64;\n        let p99_ms = metrics.p99_response_time().as_millis() as u64;\n        \n        // Response time recommendations\n        if p95_ms > config.target_p95_response_time_ms {\n            recommendations.push(format!(\n                \"P95 response time ({} ms) exceeds target. Consider: database query optimization, caching, or connection pooling\",\n                p95_ms\n            ));\n        }\n        \n        if p99_ms > config.target_p99_response_time_ms {\n            recommendations.push(format!(\n                \"P99 response time ({} ms) exceeds target. Consider: async processing, request queuing, or load balancing\",\n                p99_ms\n            ));\n        }\n        \n        // Memory recommendations\n        if metrics.memory_usage_mb > config.max_memory_usage_mb as f64 {\n            recommendations.push(format!(\n                \"Memory usage ({:.1} MB) exceeds target. Consider: memory profiling, garbage collection tuning, or data structure optimization\",\n                metrics.memory_usage_mb\n            ));\n        }\n        \n        // Cache recommendations\n        if metrics.cache_hit_ratio < config.cache_hit_ratio_target {\n            recommendations.push(format!(\n                \"Cache hit ratio ({:.2}%) below target. Consider: cache warming, TTL optimization, or cache key strategy review\",\n                metrics.cache_hit_ratio * 100.0\n            ));\n        }\n        \n        // Connection recommendations\n        if metrics.active_connections > 100 {\n            recommendations.push(\n                \"High number of active connections. Consider: connection pooling optimization or load balancing\".to_string()\n            );\n        }\n        \n        // RPS recommendations\n        if metrics.requests_per_second > 1000.0 {\n            recommendations.push(\n                \"High request rate. Consider: horizontal scaling, caching, or rate limiting\".to_string()\n            );\n        }\n        \n        if recommendations.is_empty() {\n            recommendations.push(\"Performance targets are being met. Continue monitoring.\".to_string());\n        }\n        \n        recommendations\n    }\n}\n", "modifiedCode": "pub mod response_optimizer;\npub mod caching;\npub mod memory_manager;\npub mod scaling;\n\npub use response_optimizer::*;\npub use caching::*;\npub use memory_manager::*;\npub use scaling::*;\n\nuse std::time::{Duration, Instant};\nuse std::sync::OnceLock;\nuse tracing::{info, warn};\n\n/// Global performance metrics instance\nstatic PERFORMANCE_METRICS: OnceLock<PerformanceMonitor> = OnceLock::new();\n\n/// Performance configuration for the application\n#[derive(Debug, Clone)]\npub struct PerformanceConfig {\n    /// Target response time for 95th percentile (milliseconds)\n    pub target_p95_response_time_ms: u64,\n    /// Target response time for 99th percentile (milliseconds)\n    pub target_p99_response_time_ms: u64,\n    /// Maximum allowed memory usage per instance (MB)\n    pub max_memory_usage_mb: u64,\n    /// Cache hit ratio target (0.0 to 1.0)\n    pub cache_hit_ratio_target: f64,\n    /// Enable performance monitoring\n    pub enable_monitoring: bool,\n}\n\nimpl Default for PerformanceConfig {\n    fn default() -> Self {\n        Self {\n            target_p95_response_time_ms: 200,\n            target_p99_response_time_ms: 500,\n            max_memory_usage_mb: 512,\n            cache_hit_ratio_target: 0.85,\n            enable_monitoring: true,\n        }\n    }\n}\n\n/// Performance metrics collector\n#[derive(Debug, Clone)]\npub struct PerformanceMetrics {\n    pub response_times: Vec<Duration>,\n    pub memory_usage_mb: f64,\n    pub cache_hit_ratio: f64,\n    pub active_connections: u32,\n    pub requests_per_second: f64,\n}\n\nimpl PerformanceMetrics {\n    pub fn new() -> Self {\n        Self {\n            response_times: Vec::new(),\n            memory_usage_mb: 0.0,\n            cache_hit_ratio: 0.0,\n            active_connections: 0,\n            requests_per_second: 0.0,\n        }\n    }\n\n    /// Calculate 95th percentile response time\n    pub fn p95_response_time(&self) -> Duration {\n        if self.response_times.is_empty() {\n            return Duration::from_millis(0);\n        }\n\n        let mut sorted_times = self.response_times.clone();\n        sorted_times.sort();\n        let index = (sorted_times.len() as f64 * 0.95) as usize;\n        sorted_times.get(index).copied().unwrap_or(Duration::from_millis(0))\n    }\n\n    /// Calculate 99th percentile response time\n    pub fn p99_response_time(&self) -> Duration {\n        if self.response_times.is_empty() {\n            return Duration::from_millis(0);\n        }\n\n        let mut sorted_times = self.response_times.clone();\n        sorted_times.sort();\n        let index = (sorted_times.len() as f64 * 0.99) as usize;\n        sorted_times.get(index).copied().unwrap_or(Duration::from_millis(0))\n    }\n\n    /// Calculate average response time\n    pub fn average_response_time(&self) -> Duration {\n        if self.response_times.is_empty() {\n            return Duration::from_millis(0);\n        }\n\n        let total: Duration = self.response_times.iter().sum();\n        total / self.response_times.len() as u32\n    }\n\n    /// Add a response time measurement\n    pub fn record_response_time(&mut self, duration: Duration) {\n        self.response_times.push(duration);\n        \n        // Keep only the last 1000 measurements to prevent memory growth\n        if self.response_times.len() > 1000 {\n            self.response_times.remove(0);\n        }\n    }\n\n    /// Check if performance targets are met\n    pub fn meets_targets(&self, config: &PerformanceConfig) -> bool {\n        let p95_ms = self.p95_response_time().as_millis() as u64;\n        let p99_ms = self.p99_response_time().as_millis() as u64;\n        \n        p95_ms <= config.target_p95_response_time_ms\n            && p99_ms <= config.target_p99_response_time_ms\n            && self.memory_usage_mb <= config.max_memory_usage_mb as f64\n            && self.cache_hit_ratio >= config.cache_hit_ratio_target\n    }\n}\n\nimpl Default for PerformanceMetrics {\n    fn default() -> Self {\n        Self::new()\n    }\n}\n\n/// Performance monitor that tracks and reports performance metrics\npub struct PerformanceMonitor {\n    config: PerformanceConfig,\n    metrics: PerformanceMetrics,\n    last_report: Instant,\n}\n\nimpl PerformanceMonitor {\n    pub fn new(config: PerformanceConfig) -> Self {\n        Self {\n            config,\n            metrics: PerformanceMetrics::new(),\n            last_report: Instant::now(),\n        }\n    }\n\n    /// Record a request completion\n    pub fn record_request(&mut self, duration: Duration) {\n        self.metrics.record_response_time(duration);\n        \n        // Check if we should report performance\n        if self.last_report.elapsed() >= Duration::from_secs(60) {\n            self.report_performance();\n            self.last_report = Instant::now();\n        }\n    }\n\n    /// Update memory usage\n    pub fn update_memory_usage(&mut self, usage_mb: f64) {\n        self.metrics.memory_usage_mb = usage_mb;\n    }\n\n    /// Update cache hit ratio\n    pub fn update_cache_hit_ratio(&mut self, ratio: f64) {\n        self.metrics.cache_hit_ratio = ratio;\n    }\n\n    /// Update active connections\n    pub fn update_active_connections(&mut self, count: u32) {\n        self.metrics.active_connections = count;\n    }\n\n    /// Update requests per second\n    pub fn update_requests_per_second(&mut self, rps: f64) {\n        self.metrics.requests_per_second = rps;\n    }\n\n    /// Report current performance metrics\n    pub fn report_performance(&self) {\n        if !self.config.enable_monitoring {\n            return;\n        }\n\n        let p95_ms = self.metrics.p95_response_time().as_millis();\n        let p99_ms = self.metrics.p99_response_time().as_millis();\n        let avg_ms = self.metrics.average_response_time().as_millis();\n\n        info!(\n            \"Performance Report - P95: {}ms, P99: {}ms, Avg: {}ms, Memory: {:.1}MB, Cache Hit: {:.2}%, RPS: {:.1}\",\n            p95_ms,\n            p99_ms,\n            avg_ms,\n            self.metrics.memory_usage_mb,\n            self.metrics.cache_hit_ratio * 100.0,\n            self.metrics.requests_per_second\n        );\n\n        // Check if targets are met\n        if !self.metrics.meets_targets(&self.config) {\n            warn!(\"Performance targets not met!\");\n            \n            if p95_ms > self.config.target_p95_response_time_ms as u128 {\n                warn!(\"P95 response time ({} ms) exceeds target ({} ms)\", \n                      p95_ms, self.config.target_p95_response_time_ms);\n            }\n            \n            if p99_ms > self.config.target_p99_response_time_ms as u128 {\n                warn!(\"P99 response time ({} ms) exceeds target ({} ms)\", \n                      p99_ms, self.config.target_p99_response_time_ms);\n            }\n            \n            if self.metrics.memory_usage_mb > self.config.max_memory_usage_mb as f64 {\n                warn!(\"Memory usage ({:.1} MB) exceeds target ({} MB)\", \n                      self.metrics.memory_usage_mb, self.config.max_memory_usage_mb);\n            }\n            \n            if self.metrics.cache_hit_ratio < self.config.cache_hit_ratio_target {\n                warn!(\"Cache hit ratio ({:.2}%) below target ({:.2}%)\", \n                      self.metrics.cache_hit_ratio * 100.0, \n                      self.config.cache_hit_ratio_target * 100.0);\n            }\n        }\n    }\n\n    /// Get current metrics\n    pub fn get_metrics(&self) -> &PerformanceMetrics {\n        &self.metrics\n    }\n\n    /// Get performance configuration\n    pub fn get_config(&self) -> &PerformanceConfig {\n        &self.config\n    }\n}\n\n/// Middleware for measuring request performance\npub async fn performance_middleware<B>(\n    req: axum::extract::Request<B>,\n    next: axum::middleware::Next<B>,\n) -> axum::response::Response {\n    let start = Instant::now();\n    let method = req.method().clone();\n    let uri = req.uri().clone();\n    \n    let response = next.run(req).await;\n    \n    let duration = start.elapsed();\n    let status = response.status();\n    \n    // Log slow requests\n    if duration > Duration::from_millis(200) {\n        warn!(\n            \"Slow request: {} {} - {}ms (status: {})\",\n            method,\n            uri,\n            duration.as_millis(),\n            status\n        );\n    }\n    \n    // Record metrics in performance monitor\n    let monitor = PERFORMANCE_METRICS.get_or_init(|| PerformanceMonitor::new());\n    monitor.record_request(\n        method.as_str(),\n        uri.path(),\n        status.as_u16(),\n        duration,\n    );\n    \n    response\n}\n\n/// Performance optimization recommendations\npub struct PerformanceRecommendations;\n\nimpl PerformanceRecommendations {\n    /// Generate performance optimization recommendations\n    pub fn generate(metrics: &PerformanceMetrics, config: &PerformanceConfig) -> Vec<String> {\n        let mut recommendations = Vec::new();\n        \n        let p95_ms = metrics.p95_response_time().as_millis() as u64;\n        let p99_ms = metrics.p99_response_time().as_millis() as u64;\n        \n        // Response time recommendations\n        if p95_ms > config.target_p95_response_time_ms {\n            recommendations.push(format!(\n                \"P95 response time ({} ms) exceeds target. Consider: database query optimization, caching, or connection pooling\",\n                p95_ms\n            ));\n        }\n        \n        if p99_ms > config.target_p99_response_time_ms {\n            recommendations.push(format!(\n                \"P99 response time ({} ms) exceeds target. Consider: async processing, request queuing, or load balancing\",\n                p99_ms\n            ));\n        }\n        \n        // Memory recommendations\n        if metrics.memory_usage_mb > config.max_memory_usage_mb as f64 {\n            recommendations.push(format!(\n                \"Memory usage ({:.1} MB) exceeds target. Consider: memory profiling, garbage collection tuning, or data structure optimization\",\n                metrics.memory_usage_mb\n            ));\n        }\n        \n        // Cache recommendations\n        if metrics.cache_hit_ratio < config.cache_hit_ratio_target {\n            recommendations.push(format!(\n                \"Cache hit ratio ({:.2}%) below target. Consider: cache warming, TTL optimization, or cache key strategy review\",\n                metrics.cache_hit_ratio * 100.0\n            ));\n        }\n        \n        // Connection recommendations\n        if metrics.active_connections > 100 {\n            recommendations.push(\n                \"High number of active connections. Consider: connection pooling optimization or load balancing\".to_string()\n            );\n        }\n        \n        // RPS recommendations\n        if metrics.requests_per_second > 1000.0 {\n            recommendations.push(\n                \"High request rate. Consider: horizontal scaling, caching, or rate limiting\".to_string()\n            );\n        }\n        \n        if recommendations.is_empty() {\n            recommendations.push(\"Performance targets are being met. Continue monitoring.\".to_string());\n        }\n        \n        recommendations\n    }\n}\n"}