{"/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/yallist/LICENSE.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/yallist/LICENSE.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/yallist/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/yallist/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/yallist/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/yallist/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/yallist/dist/esm/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/yallist/dist/esm/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/yallist/dist/esm/index.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/yallist/dist/esm/index.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/yallist/dist/esm/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/yallist/dist/esm/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/yallist/dist/esm/index.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/yallist/dist/esm/index.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/yallist/dist/esm/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/yallist/dist/esm/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/yallist/dist/commonjs/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/yallist/dist/commonjs/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/yallist/dist/commonjs/index.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/yallist/dist/commonjs/index.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/yallist/dist/commonjs/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/yallist/dist/commonjs/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/yallist/dist/commonjs/index.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/yallist/dist/commonjs/index.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/yallist/dist/commonjs/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/yallist/dist/commonjs/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/client.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/client.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/types/customEvent.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/types/customEvent.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/types/hmrPayload.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/types/hmrPayload.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/types/hot.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/types/hot.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/types/import-meta.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/types/import-meta.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/types/importGlob.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/types/importGlob.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/types/importMeta.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/types/importMeta.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/types/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/types/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/types/internal/cssPreprocessorOptions.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/types/internal/cssPreprocessorOptions.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/types/internal/lightningcssOptions.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/types/internal/lightningcssOptions.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/types/internal/terserOptions.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/types/internal/terserOptions.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/misc/false.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/misc/false.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/misc/true.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/misc/true.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/dist/node/moduleRunnerTransport-BWUZBVLX.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/dist/node/moduleRunnerTransport-BWUZBVLX.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/dist/node/chunks/dep-Drtntmtt.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/dist/node/chunks/dep-Drtntmtt.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/dist/client/env.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/dist/client/env.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/bin/vite.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/bin/vite.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tinyglobby/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tinyglobby/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tinyglobby/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tinyglobby/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tinyglobby/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tinyglobby/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tinyglobby/dist/index.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tinyglobby/dist/index.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tinyglobby/dist/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tinyglobby/dist/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tinyglobby/dist/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tinyglobby/dist/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tinyglobby/dist/index.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tinyglobby/dist/index.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/create.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/create.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/create.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/create.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/create.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/create.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/create.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/create.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/cwd-error.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/cwd-error.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/cwd-error.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/cwd-error.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/cwd-error.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/cwd-error.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/cwd-error.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/cwd-error.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/extract.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/extract.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/extract.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/extract.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/extract.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/extract.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/extract.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/extract.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/get-write-flag.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/get-write-flag.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/get-write-flag.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/get-write-flag.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/get-write-flag.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/get-write-flag.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/get-write-flag.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/get-write-flag.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/header.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/header.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/header.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/header.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/header.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/header.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/header.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/header.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/index.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/index.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/index.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/index.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/large-numbers.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/large-numbers.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/large-numbers.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/large-numbers.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/large-numbers.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/large-numbers.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/large-numbers.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/large-numbers.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/list.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/list.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/list.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/list.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/list.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/list.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/list.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/list.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/make-command.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/make-command.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/make-command.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/make-command.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/make-command.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/make-command.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/make-command.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/make-command.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/mkdir.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/mkdir.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/mkdir.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/mkdir.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/mkdir.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/mkdir.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/mkdir.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/mkdir.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/mode-fix.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/mode-fix.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/mode-fix.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/mode-fix.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/mode-fix.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/mode-fix.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/mode-fix.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/mode-fix.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/normalize-unicode.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/normalize-unicode.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/normalize-unicode.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/normalize-unicode.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/normalize-unicode.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/normalize-unicode.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/normalize-unicode.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/normalize-unicode.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/normalize-windows-path.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/normalize-windows-path.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/normalize-windows-path.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/normalize-windows-path.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/normalize-windows-path.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/normalize-windows-path.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/normalize-windows-path.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/normalize-windows-path.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/options.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/options.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/options.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/options.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/options.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/options.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/options.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/options.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/pack.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/pack.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/pack.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/pack.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/pack.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/pack.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/pack.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/pack.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/parse.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/parse.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/parse.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/parse.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/parse.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/parse.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/parse.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/parse.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/path-reservations.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/path-reservations.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/path-reservations.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/path-reservations.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/path-reservations.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/path-reservations.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/path-reservations.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/path-reservations.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/pax.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/pax.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/pax.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/pax.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/pax.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/pax.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/pax.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/pax.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/read-entry.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/read-entry.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/read-entry.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/read-entry.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/read-entry.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/read-entry.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/read-entry.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/read-entry.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/replace.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/replace.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/replace.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/replace.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/replace.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/replace.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/replace.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/replace.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/strip-absolute-path.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/strip-absolute-path.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/strip-absolute-path.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/strip-absolute-path.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/strip-absolute-path.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/strip-absolute-path.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/strip-absolute-path.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/strip-absolute-path.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/strip-trailing-slashes.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/strip-trailing-slashes.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/strip-trailing-slashes.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/strip-trailing-slashes.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/strip-trailing-slashes.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/strip-trailing-slashes.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/strip-trailing-slashes.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/strip-trailing-slashes.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/symlink-error.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/symlink-error.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/symlink-error.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/symlink-error.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/symlink-error.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/symlink-error.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/symlink-error.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/symlink-error.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/types.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/types.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/types.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/types.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/types.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/types.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/types.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/types.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/unpack.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/unpack.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/unpack.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/unpack.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/unpack.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/unpack.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/unpack.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/unpack.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/update.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/update.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/update.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/update.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/update.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/update.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/update.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/update.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/warn-method.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/warn-method.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/warn-method.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/warn-method.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/warn-method.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/warn-method.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/warn-method.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/warn-method.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/winchars.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/winchars.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/winchars.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/winchars.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/winchars.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/winchars.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/winchars.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/winchars.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/write-entry.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/write-entry.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/write-entry.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/write-entry.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/write-entry.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/write-entry.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/write-entry.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/write-entry.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/create.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/create.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/create.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/create.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/create.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/create.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/create.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/create.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/cwd-error.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/cwd-error.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/cwd-error.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/cwd-error.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/cwd-error.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/cwd-error.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/cwd-error.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/cwd-error.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/extract.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/extract.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/extract.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/extract.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/extract.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/extract.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/extract.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/extract.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/get-write-flag.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/get-write-flag.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/get-write-flag.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/get-write-flag.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/get-write-flag.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/get-write-flag.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/get-write-flag.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/get-write-flag.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/header.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/header.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/header.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/header.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/header.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/header.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/header.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/header.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/index.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/index.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/index.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/index.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/large-numbers.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/large-numbers.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/large-numbers.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/large-numbers.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/large-numbers.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/large-numbers.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/large-numbers.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/large-numbers.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/list.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/list.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/list.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/list.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/list.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/list.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/list.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/list.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/make-command.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/make-command.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/make-command.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/make-command.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/make-command.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/make-command.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/make-command.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/make-command.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/mkdir.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/mkdir.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/mkdir.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/mkdir.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/mkdir.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/mkdir.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/mkdir.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/mkdir.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/mode-fix.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/mode-fix.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/mode-fix.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/mode-fix.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/mode-fix.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/mode-fix.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/mode-fix.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/mode-fix.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/normalize-unicode.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/normalize-unicode.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/normalize-unicode.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/normalize-unicode.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/normalize-unicode.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/normalize-unicode.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/normalize-unicode.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/normalize-unicode.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/normalize-windows-path.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/normalize-windows-path.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/normalize-windows-path.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/normalize-windows-path.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/normalize-windows-path.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/normalize-windows-path.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/normalize-windows-path.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/normalize-windows-path.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/options.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/options.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/options.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/options.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/options.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/options.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/options.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/options.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/pack.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/pack.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/pack.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/pack.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/pack.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/pack.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/pack.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/pack.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/parse.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/parse.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/parse.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/parse.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/parse.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/parse.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/parse.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/parse.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/path-reservations.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/path-reservations.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/path-reservations.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/path-reservations.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/path-reservations.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/path-reservations.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/path-reservations.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/path-reservations.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/pax.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/pax.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/pax.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/pax.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/pax.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/pax.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/pax.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/pax.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/read-entry.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/read-entry.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/read-entry.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/read-entry.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/read-entry.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/read-entry.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/read-entry.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/read-entry.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/replace.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/replace.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/replace.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/replace.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/replace.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/replace.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/replace.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/replace.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/strip-absolute-path.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/strip-absolute-path.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/strip-absolute-path.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/strip-absolute-path.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/strip-absolute-path.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/strip-absolute-path.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/strip-absolute-path.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/strip-absolute-path.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/strip-trailing-slashes.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/strip-trailing-slashes.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/strip-trailing-slashes.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/strip-trailing-slashes.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/strip-trailing-slashes.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/strip-trailing-slashes.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/strip-trailing-slashes.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/strip-trailing-slashes.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/symlink-error.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/symlink-error.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/symlink-error.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/symlink-error.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/symlink-error.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/symlink-error.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/symlink-error.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/symlink-error.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/types.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/types.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/types.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/types.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/types.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/types.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/types.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/types.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/unpack.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/unpack.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/unpack.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/unpack.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/unpack.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/unpack.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/unpack.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/unpack.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/update.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/update.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/update.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/update.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/update.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/update.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/update.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/update.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/warn-method.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/warn-method.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/warn-method.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/warn-method.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/warn-method.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/warn-method.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/warn-method.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/warn-method.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/winchars.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/winchars.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/winchars.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/winchars.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/winchars.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/winchars.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/winchars.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/winchars.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/write-entry.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/write-entry.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/write-entry.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/write-entry.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/write-entry.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/write-entry.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/write-entry.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/write-entry.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tapable/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tapable/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tapable/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tapable/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tapable/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tapable/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tapable/tapable.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tapable/tapable.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tapable/lib/AsyncParallelBailHook.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tapable/lib/AsyncParallelBailHook.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tapable/lib/AsyncParallelHook.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tapable/lib/AsyncParallelHook.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tapable/lib/AsyncSeriesBailHook.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tapable/lib/AsyncSeriesBailHook.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tapable/lib/AsyncSeriesHook.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tapable/lib/AsyncSeriesHook.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tapable/lib/AsyncSeriesLoopHook.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tapable/lib/AsyncSeriesLoopHook.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tapable/lib/AsyncSeriesWaterfallHook.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tapable/lib/AsyncSeriesWaterfallHook.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tapable/lib/Hook.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tapable/lib/Hook.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tapable/lib/HookCodeFactory.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tapable/lib/HookCodeFactory.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tapable/lib/HookMap.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tapable/lib/HookMap.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tapable/lib/MultiHook.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tapable/lib/MultiHook.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tapable/lib/SyncBailHook.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tapable/lib/SyncBailHook.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tapable/lib/SyncHook.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tapable/lib/SyncHook.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tapable/lib/SyncLoopHook.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tapable/lib/SyncLoopHook.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tapable/lib/SyncWaterfallHook.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tapable/lib/SyncWaterfallHook.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tapable/lib/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tapable/lib/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tapable/lib/util-browser.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tapable/lib/util-browser.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/index.css": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/index.css"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/preflight.css": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/preflight.css"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/theme.css": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/theme.css"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/utilities.css": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/utilities.css"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/chunk-4WXWQT6Y.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/chunk-4WXWQT6Y.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/chunk-G32FJCSR.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/chunk-G32FJCSR.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/chunk-HTB5LLOP.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/chunk-HTB5LLOP.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/colors-b_6i0Oi7.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/colors-b_6i0Oi7.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/colors.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/colors.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/colors.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/colors.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/colors.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/colors.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/colors.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/colors.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/default-theme.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/default-theme.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/default-theme.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/default-theme.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/default-theme.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/default-theme.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/default-theme.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/default-theme.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/flatten-color-palette.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/flatten-color-palette.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/flatten-color-palette.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/flatten-color-palette.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/flatten-color-palette.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/flatten-color-palette.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/flatten-color-palette.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/flatten-color-palette.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/lib.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/lib.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/lib.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/lib.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/lib.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/lib.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/lib.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/lib.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/plugin.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/plugin.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/plugin.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/plugin.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/plugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/plugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/plugin.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/plugin.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/resolve-config-BIFUA2FY.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/resolve-config-BIFUA2FY.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/resolve-config-QUZ9b-Gn.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/resolve-config-QUZ9b-Gn.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/types-B254mqw1.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/types-B254mqw1.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/source-map-js/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/source-map-js/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/source-map-js/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/source-map-js/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/source-map-js/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/source-map-js/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/source-map-js/source-map.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/source-map-js/source-map.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/source-map-js/source-map.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/source-map-js/source-map.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/source-map-js/lib/array-set.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/source-map-js/lib/array-set.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/source-map-js/lib/base64-vlq.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/source-map-js/lib/base64-vlq.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/source-map-js/lib/base64.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/source-map-js/lib/base64.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/source-map-js/lib/binary-search.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/source-map-js/lib/binary-search.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/source-map-js/lib/mapping-list.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/source-map-js/lib/mapping-list.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/source-map-js/lib/quick-sort.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/source-map-js/lib/quick-sort.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/source-map-js/lib/source-map-consumer.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/source-map-js/lib/source-map-consumer.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/source-map-js/lib/source-map-consumer.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/source-map-js/lib/source-map-consumer.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/source-map-js/lib/source-map-generator.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/source-map-js/lib/source-map-generator.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/source-map-js/lib/source-map-generator.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/source-map-js/lib/source-map-generator.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/source-map-js/lib/source-node.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/source-map-js/lib/source-node.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/source-map-js/lib/source-node.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/source-map-js/lib/source-node.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/source-map-js/lib/util.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/source-map-js/lib/util.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/LICENSE.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/LICENSE.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/getLogFilter.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/getLogFilter.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/loadConfigFile.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/loadConfigFile.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/parseAst.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/parseAst.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/rollup.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/rollup.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/es/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/es/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/at-rule.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/at-rule.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/at-rule.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/at-rule.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/comment.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/comment.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/comment.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/comment.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/container.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/container.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/container.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/container.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/css-syntax-error.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/css-syntax-error.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/css-syntax-error.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/css-syntax-error.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/declaration.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/declaration.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/declaration.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/declaration.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/document.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/document.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/document.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/document.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/fromJSON.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/fromJSON.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/fromJSON.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/fromJSON.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/input.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/input.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/input.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/input.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/lazy-result.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/lazy-result.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/lazy-result.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/lazy-result.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/list.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/list.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/list.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/list.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/map-generator.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/map-generator.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/no-work-result.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/no-work-result.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/no-work-result.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/no-work-result.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/node.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/node.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/node.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/node.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/parse.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/parse.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/parse.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/parse.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/parser.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/parser.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/postcss.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/postcss.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/postcss.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/postcss.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/postcss.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/postcss.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/postcss.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/postcss.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/previous-map.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/previous-map.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/previous-map.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/previous-map.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/processor.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/processor.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/processor.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/processor.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/result.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/result.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/result.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/result.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/root.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/root.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/root.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/root.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/rule.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/rule.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/rule.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/rule.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/stringifier.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/stringifier.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/stringifier.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/stringifier.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/stringify.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/stringify.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/stringify.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/stringify.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/symbols.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/symbols.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/terminal-highlight.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/terminal-highlight.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/tokenize.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/tokenize.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/warn-once.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/warn-once.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/warning.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/warning.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/warning.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/warning.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/picomatch/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/picomatch/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/picomatch/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/picomatch/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/picomatch/posix.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/picomatch/posix.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/picomatch/lib/parse.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/picomatch/lib/parse.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/picomatch/lib/picomatch.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/picomatch/lib/picomatch.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/picomatch/lib/scan.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/picomatch/lib/scan.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/picomatch/lib/utils.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/picomatch/lib/utils.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/picocolors/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/picocolors/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/picocolors/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/picocolors/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/picocolors/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/picocolors/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/picocolors/picocolors.browser.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/picocolors/picocolors.browser.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/picocolors/picocolors.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/picocolors/picocolors.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/picocolors/picocolors.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/picocolors/picocolors.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/picocolors/types.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/picocolors/types.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/index.browser.cjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/index.browser.cjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/index.browser.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/index.browser.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/index.cjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/index.cjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/index.d.cts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/index.d.cts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/nanoid.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/nanoid.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/url-alphabet/index.cjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/url-alphabet/index.cjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/url-alphabet/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/url-alphabet/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/url-alphabet/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/url-alphabet/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/non-secure/index.cjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/non-secure/index.cjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/non-secure/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/non-secure/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/non-secure/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/non-secure/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/non-secure/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/non-secure/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/bin/nanoid.cjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/bin/nanoid.cjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/async/index.browser.cjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/async/index.browser.cjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/async/index.browser.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/async/index.browser.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/async/index.cjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/async/index.cjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/async/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/async/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/async/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/async/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/async/index.native.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/async/index.native.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/async/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/async/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/readme.markdown": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/readme.markdown"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/find-made.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/find-made.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/find-made.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/find-made.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/find-made.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/find-made.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/find-made.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/find-made.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/index.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/index.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/index.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/index.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/mkdirp-manual.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/mkdirp-manual.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/mkdirp-manual.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/mkdirp-manual.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/mkdirp-manual.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/mkdirp-manual.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/mkdirp-manual.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/mkdirp-manual.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/mkdirp-native.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/mkdirp-native.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/mkdirp-native.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/mkdirp-native.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/mkdirp-native.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/mkdirp-native.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/mkdirp-native.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/mkdirp-native.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/opts-arg.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/opts-arg.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/opts-arg.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/opts-arg.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/opts-arg.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/opts-arg.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/opts-arg.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/opts-arg.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/path-arg.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/path-arg.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/path-arg.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/path-arg.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/path-arg.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/path-arg.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/path-arg.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/path-arg.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/use-native.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/use-native.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/use-native.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/use-native.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/use-native.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/use-native.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/use-native.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/use-native.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/bin.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/bin.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/bin.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/bin.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/bin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/bin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/bin.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/bin.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/find-made.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/find-made.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/find-made.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/find-made.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/find-made.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/find-made.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/find-made.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/find-made.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/index.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/index.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/index.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/index.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/mkdirp-manual.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/mkdirp-manual.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/mkdirp-manual.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/mkdirp-manual.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/mkdirp-manual.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/mkdirp-manual.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/mkdirp-manual.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/mkdirp-manual.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/mkdirp-native.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/mkdirp-native.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/mkdirp-native.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/mkdirp-native.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/mkdirp-native.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/mkdirp-native.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/mkdirp-native.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/mkdirp-native.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/opts-arg.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/opts-arg.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/opts-arg.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/opts-arg.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/opts-arg.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/opts-arg.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/opts-arg.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/opts-arg.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/path-arg.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/path-arg.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/path-arg.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/path-arg.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/path-arg.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/path-arg.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/path-arg.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/path-arg.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/use-native.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/use-native.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/use-native.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/use-native.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/use-native.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/use-native.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/use-native.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/use-native.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/dist/esm/constants.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/dist/esm/constants.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/dist/esm/constants.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/dist/esm/constants.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/dist/esm/constants.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/dist/esm/constants.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/dist/esm/constants.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/dist/esm/constants.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/dist/esm/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/dist/esm/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/dist/esm/index.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/dist/esm/index.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/dist/esm/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/dist/esm/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/dist/esm/index.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/dist/esm/index.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/dist/esm/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/dist/esm/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/dist/commonjs/constants.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/dist/commonjs/constants.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/dist/commonjs/constants.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/dist/commonjs/constants.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/dist/commonjs/constants.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/dist/commonjs/constants.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/dist/commonjs/constants.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/dist/commonjs/constants.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/dist/commonjs/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/dist/commonjs/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/dist/commonjs/index.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/dist/commonjs/index.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/dist/commonjs/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/dist/commonjs/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/dist/commonjs/index.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/dist/commonjs/index.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/dist/commonjs/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/dist/commonjs/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minipass/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minipass/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minipass/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minipass/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minipass/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minipass/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minipass/dist/esm/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minipass/dist/esm/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minipass/dist/esm/index.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minipass/dist/esm/index.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minipass/dist/esm/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minipass/dist/esm/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minipass/dist/esm/index.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minipass/dist/esm/index.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minipass/dist/esm/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minipass/dist/esm/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minipass/dist/commonjs/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minipass/dist/commonjs/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minipass/dist/commonjs/index.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minipass/dist/commonjs/index.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minipass/dist/commonjs/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minipass/dist/commonjs/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minipass/dist/commonjs/index.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minipass/dist/commonjs/index.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minipass/dist/commonjs/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minipass/dist/commonjs/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/magic-string/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/magic-string/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/magic-string/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/magic-string/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/magic-string/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/magic-string/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/magic-string/dist/magic-string.cjs.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/magic-string/dist/magic-string.cjs.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/magic-string/dist/magic-string.cjs.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/magic-string/dist/magic-string.cjs.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/magic-string/dist/magic-string.cjs.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/magic-string/dist/magic-string.cjs.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/magic-string/dist/magic-string.es.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/magic-string/dist/magic-string.es.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/magic-string/dist/magic-string.es.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/magic-string/dist/magic-string.es.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/magic-string/dist/magic-string.es.mjs.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/magic-string/dist/magic-string.es.mjs.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/magic-string/dist/magic-string.umd.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/magic-string/dist/magic-string.umd.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/magic-string/dist/magic-string.umd.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/magic-string/dist/magic-string.umd.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/lightningcss/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/lightningcss/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/lightningcss/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/lightningcss/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/lightningcss/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/lightningcss/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/lightningcss/node/ast.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/lightningcss/node/ast.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/lightningcss/node/ast.js.flow": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/lightningcss/node/ast.js.flow"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/lightningcss/node/browserslistToTargets.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/lightningcss/node/browserslistToTargets.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/lightningcss/node/composeVisitors.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/lightningcss/node/composeVisitors.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/lightningcss/node/flags.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/lightningcss/node/flags.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/lightningcss/node/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/lightningcss/node/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/lightningcss/node/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/lightningcss/node/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/lightningcss/node/index.js.flow": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/lightningcss/node/index.js.flow"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/lightningcss/node/index.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/lightningcss/node/index.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/lightningcss/node/targets.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/lightningcss/node/targets.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/lightningcss/node/targets.js.flow": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/lightningcss/node/targets.js.flow"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/jiti/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/jiti/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/jiti/lib/jiti-cli.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/jiti/lib/jiti-cli.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/jiti/lib/jiti-native.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/jiti/lib/jiti-native.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/jiti/lib/jiti-register.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/jiti/lib/jiti-register.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/jiti/lib/jiti-register.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/jiti/lib/jiti-register.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/jiti/lib/jiti.cjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/jiti/lib/jiti.cjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/jiti/lib/jiti.d.cts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/jiti/lib/jiti.d.cts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/jiti/lib/jiti.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/jiti/lib/jiti.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/jiti/lib/jiti.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/jiti/lib/jiti.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/graceful-fs/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/graceful-fs/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/graceful-fs/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/graceful-fs/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/graceful-fs/clone.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/graceful-fs/clone.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/graceful-fs/graceful-fs.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/graceful-fs/graceful-fs.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/graceful-fs/legacy-streams.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/graceful-fs/legacy-streams.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/graceful-fs/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/graceful-fs/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/graceful-fs/polyfills.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/graceful-fs/polyfills.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/index.cjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/index.cjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/index.d.cts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/index.d.cts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/index.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/index.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/index.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/index.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/types.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/types.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/types.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/types.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/utils.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/utils.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/utils.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/utils.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/builder/api-builder.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/builder/api-builder.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/builder/api-builder.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/builder/api-builder.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/builder/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/builder/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/builder/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/builder/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/async.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/async.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/async.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/async.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/counter.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/counter.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/counter.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/counter.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/queue.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/queue.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/queue.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/queue.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/sync.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/sync.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/sync.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/sync.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/walker.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/walker.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/walker.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/walker.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/functions/get-array.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/functions/get-array.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/functions/get-array.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/functions/get-array.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/functions/group-files.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/functions/group-files.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/functions/group-files.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/functions/group-files.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/functions/invoke-callback.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/functions/invoke-callback.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/functions/invoke-callback.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/functions/invoke-callback.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/functions/join-path.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/functions/join-path.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/functions/join-path.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/functions/join-path.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/functions/push-directory.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/functions/push-directory.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/functions/push-directory.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/functions/push-directory.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/functions/push-file.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/functions/push-file.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/functions/push-file.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/functions/push-file.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/functions/resolve-symlink.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/functions/resolve-symlink.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/functions/resolve-symlink.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/functions/resolve-symlink.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/functions/walk-directory.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/functions/walk-directory.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/functions/walk-directory.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/functions/walk-directory.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/esbuild/LICENSE.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/esbuild/LICENSE.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/esbuild/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/esbuild/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/esbuild/install.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/esbuild/install.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/esbuild/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/esbuild/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/esbuild/lib/main.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/esbuild/lib/main.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/esbuild/lib/main.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/esbuild/lib/main.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/AppendPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/AppendPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/CloneBasenamePlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/CloneBasenamePlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/ConditionalPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/ConditionalPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/DirectoryExistsPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/DirectoryExistsPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/ExtensionAliasPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/ExtensionAliasPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/FileExistsPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/FileExistsPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/JoinRequestPartPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/JoinRequestPartPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/JoinRequestPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/JoinRequestPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/MainFieldPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/MainFieldPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/ModulesInHierachicDirectoriesPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/ModulesInHierachicDirectoriesPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/ModulesInHierarchicalDirectoriesPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/ModulesInHierarchicalDirectoriesPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/ModulesInRootPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/ModulesInRootPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/NextPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/NextPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/ParsePlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/ParsePlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/PnpPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/PnpPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/RestrictionsPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/RestrictionsPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/ResultPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/ResultPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/RootsPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/RootsPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/SelfReferencePlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/SelfReferencePlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/SyncAsyncFileSystemDecorator.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/SyncAsyncFileSystemDecorator.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/TryNextPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/TryNextPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/UnsafeCachePlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/UnsafeCachePlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/UseFilePlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/UseFilePlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/createInnerContext.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/createInnerContext.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/forEachBail.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/forEachBail.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/getInnerRequest.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/getInnerRequest.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/getPaths.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/getPaths.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/util/entrypoints.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/util/entrypoints.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/util/identifier.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/util/identifier.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/util/module-browser.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/util/module-browser.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/util/process-browser.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/util/process-browser.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/detect-libc/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/detect-libc/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/detect-libc/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/detect-libc/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/detect-libc/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/detect-libc/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/detect-libc/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/detect-libc/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/detect-libc/lib/detect-libc.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/detect-libc/lib/detect-libc.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/detect-libc/lib/filesystem.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/detect-libc/lib/filesystem.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/detect-libc/lib/process.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/detect-libc/lib/process.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/chownr/LICENSE.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/chownr/LICENSE.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/chownr/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/chownr/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/chownr/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/chownr/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/chownr/dist/esm/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/chownr/dist/esm/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/chownr/dist/esm/index.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/chownr/dist/esm/index.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/chownr/dist/esm/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/chownr/dist/esm/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/chownr/dist/esm/index.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/chownr/dist/esm/index.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/chownr/dist/esm/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/chownr/dist/esm/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/chownr/dist/commonjs/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/chownr/dist/commonjs/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/chownr/dist/commonjs/index.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/chownr/dist/commonjs/index.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/chownr/dist/commonjs/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/chownr/dist/commonjs/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/chownr/dist/commonjs/index.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/chownr/dist/commonjs/index.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/chownr/dist/commonjs/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/chownr/dist/commonjs/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@types/estree/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@types/estree/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@types/estree/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@types/estree/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@types/estree/flow.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@types/estree/flow.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@types/estree/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@types/estree/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@types/estree/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@types/estree/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/oxide/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/oxide/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/oxide/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/oxide/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/oxide/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/oxide/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/oxide/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/oxide/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/oxide/scripts/install.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/oxide/scripts/install.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/node/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/node/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/node/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/node/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/node/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/node/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/node/dist/esm-cache.loader.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/node/dist/esm-cache.loader.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/node/dist/esm-cache.loader.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/node/dist/esm-cache.loader.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/node/dist/index.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/node/dist/index.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/node/dist/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/node/dist/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/node/dist/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/node/dist/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/node/dist/index.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/node/dist/index.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/node/dist/require-cache.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/node/dist/require-cache.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/node/dist/require-cache.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/node/dist/require-cache.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@rollup/rollup-darwin-arm64/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@rollup/rollup-darwin-arm64/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@rollup/rollup-darwin-arm64/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@rollup/rollup-darwin-arm64/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/binary-search.d.cts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/binary-search.d.cts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/binary-search.d.cts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/binary-search.d.cts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/binary-search.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/binary-search.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/binary-search.d.mts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/binary-search.d.mts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/by-source.d.cts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/by-source.d.cts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/by-source.d.cts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/by-source.d.cts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/by-source.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/by-source.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/by-source.d.mts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/by-source.d.mts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/flatten-map.d.cts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/flatten-map.d.cts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/flatten-map.d.cts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/flatten-map.d.cts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/flatten-map.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/flatten-map.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/flatten-map.d.mts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/flatten-map.d.mts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/resolve.d.cts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/resolve.d.cts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/resolve.d.cts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/resolve.d.cts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/resolve.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/resolve.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/resolve.d.mts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/resolve.d.mts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/sort.d.cts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/sort.d.cts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/sort.d.cts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/sort.d.cts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/sort.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/sort.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/sort.d.mts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/sort.d.mts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/sourcemap-segment.d.cts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/sourcemap-segment.d.cts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/sourcemap-segment.d.cts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/sourcemap-segment.d.cts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/sourcemap-segment.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/sourcemap-segment.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/sourcemap-segment.d.mts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/sourcemap-segment.d.mts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/strip-filename.d.cts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/strip-filename.d.cts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/strip-filename.d.cts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/strip-filename.d.cts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/strip-filename.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/strip-filename.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/strip-filename.d.mts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/strip-filename.d.mts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/trace-mapping.d.cts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/trace-mapping.d.cts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/trace-mapping.d.cts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/trace-mapping.d.cts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/trace-mapping.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/trace-mapping.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/trace-mapping.d.mts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/trace-mapping.d.mts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/types.d.cts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/types.d.cts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/types.d.cts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/types.d.cts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/types.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/types.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/types.d.mts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/types.d.mts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/src/binary-search.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/src/binary-search.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/src/by-source.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/src/by-source.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/src/flatten-map.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/src/flatten-map.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/src/resolve.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/src/resolve.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/src/sort.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/src/sort.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/src/sourcemap-segment.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/src/sourcemap-segment.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/src/strip-filename.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/src/strip-filename.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/src/trace-mapping.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/src/trace-mapping.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/src/types.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/src/types.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/dist/trace-mapping.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/dist/trace-mapping.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/dist/trace-mapping.mjs.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/dist/trace-mapping.mjs.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/dist/trace-mapping.umd.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/dist/trace-mapping.umd.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/dist/trace-mapping.umd.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/dist/trace-mapping.umd.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/types/scopes.d.cts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/types/scopes.d.cts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/types/scopes.d.cts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/types/scopes.d.cts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/types/scopes.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/types/scopes.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/types/scopes.d.mts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/types/scopes.d.mts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/types/sourcemap-codec.d.cts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/types/sourcemap-codec.d.cts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/types/sourcemap-codec.d.cts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/types/sourcemap-codec.d.cts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/types/sourcemap-codec.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/types/sourcemap-codec.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/types/sourcemap-codec.d.mts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/types/sourcemap-codec.d.mts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/types/strings.d.cts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/types/strings.d.cts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/types/strings.d.cts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/types/strings.d.cts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/types/strings.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/types/strings.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/types/strings.d.mts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/types/strings.d.mts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/types/vlq.d.cts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/types/vlq.d.cts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/types/vlq.d.cts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/types/vlq.d.cts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/types/vlq.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/types/vlq.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/types/vlq.d.mts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/types/vlq.d.mts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/src/scopes.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/src/scopes.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/src/sourcemap-codec.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/src/sourcemap-codec.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/src/strings.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/src/strings.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/src/vlq.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/src/vlq.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/dist/sourcemap-codec.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/dist/sourcemap-codec.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/dist/sourcemap-codec.mjs.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/dist/sourcemap-codec.mjs.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/dist/sourcemap-codec.umd.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/dist/sourcemap-codec.umd.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/dist/sourcemap-codec.umd.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/dist/sourcemap-codec.umd.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/resolve-uri/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/resolve-uri/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/resolve-uri/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/resolve-uri/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/resolve-uri/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/resolve-uri/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/resolve-uri/dist/resolve-uri.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/resolve-uri/dist/resolve-uri.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/resolve-uri/dist/resolve-uri.mjs.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/resolve-uri/dist/resolve-uri.mjs.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/resolve-uri/dist/resolve-uri.umd.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/resolve-uri/dist/resolve-uri.umd.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/resolve-uri/dist/resolve-uri.umd.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/resolve-uri/dist/resolve-uri.umd.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/resolve-uri/dist/types/resolve-uri.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/resolve-uri/dist/types/resolve-uri.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/types/gen-mapping.d.cts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/types/gen-mapping.d.cts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/types/gen-mapping.d.cts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/types/gen-mapping.d.cts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/types/gen-mapping.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/types/gen-mapping.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/types/gen-mapping.d.mts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/types/gen-mapping.d.mts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/types/set-array.d.cts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/types/set-array.d.cts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/types/set-array.d.cts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/types/set-array.d.cts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/types/set-array.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/types/set-array.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/types/set-array.d.mts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/types/set-array.d.mts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/types/sourcemap-segment.d.cts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/types/sourcemap-segment.d.cts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/types/sourcemap-segment.d.cts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/types/sourcemap-segment.d.cts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/types/sourcemap-segment.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/types/sourcemap-segment.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/types/sourcemap-segment.d.mts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/types/sourcemap-segment.d.mts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/types/types.d.cts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/types/types.d.cts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/types/types.d.cts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/types/types.d.cts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/types/types.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/types/types.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/types/types.d.mts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/types/types.d.mts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/src/gen-mapping.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/src/gen-mapping.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/src/set-array.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/src/set-array.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/src/sourcemap-segment.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/src/sourcemap-segment.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/src/types.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/src/types.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/dist/gen-mapping.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/dist/gen-mapping.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/dist/gen-mapping.mjs.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/dist/gen-mapping.mjs.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/dist/gen-mapping.umd.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/dist/gen-mapping.umd.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/dist/gen-mapping.umd.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/dist/gen-mapping.umd.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@isaacs/fs-minipass/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@isaacs/fs-minipass/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@isaacs/fs-minipass/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@isaacs/fs-minipass/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@isaacs/fs-minipass/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@isaacs/fs-minipass/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@isaacs/fs-minipass/dist/esm/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@isaacs/fs-minipass/dist/esm/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@isaacs/fs-minipass/dist/esm/index.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@isaacs/fs-minipass/dist/esm/index.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@isaacs/fs-minipass/dist/esm/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@isaacs/fs-minipass/dist/esm/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@isaacs/fs-minipass/dist/esm/index.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@isaacs/fs-minipass/dist/esm/index.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@isaacs/fs-minipass/dist/esm/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@isaacs/fs-minipass/dist/esm/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@isaacs/fs-minipass/dist/commonjs/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@isaacs/fs-minipass/dist/commonjs/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@isaacs/fs-minipass/dist/commonjs/index.d.ts.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@isaacs/fs-minipass/dist/commonjs/index.d.ts.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@isaacs/fs-minipass/dist/commonjs/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@isaacs/fs-minipass/dist/commonjs/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@isaacs/fs-minipass/dist/commonjs/index.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@isaacs/fs-minipass/dist/commonjs/index.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@isaacs/fs-minipass/dist/commonjs/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@isaacs/fs-minipass/dist/commonjs/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@esbuild/darwin-arm64/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@esbuild/darwin-arm64/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@ampproject/remapping/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@ampproject/remapping/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@ampproject/remapping/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@ampproject/remapping/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@ampproject/remapping/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@ampproject/remapping/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@ampproject/remapping/dist/remapping.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@ampproject/remapping/dist/remapping.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@ampproject/remapping/dist/remapping.mjs.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@ampproject/remapping/dist/remapping.mjs.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@ampproject/remapping/dist/remapping.umd.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@ampproject/remapping/dist/remapping.umd.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@ampproject/remapping/dist/remapping.umd.js.map": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@ampproject/remapping/dist/remapping.umd.js.map"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@ampproject/remapping/dist/types/build-source-map-tree.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@ampproject/remapping/dist/types/build-source-map-tree.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@ampproject/remapping/dist/types/remapping.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@ampproject/remapping/dist/types/remapping.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@ampproject/remapping/dist/types/source-map-tree.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@ampproject/remapping/dist/types/source-map-tree.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@ampproject/remapping/dist/types/source-map.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@ampproject/remapping/dist/types/source-map.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@ampproject/remapping/dist/types/types.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@ampproject/remapping/dist/types/types.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/.augment/rules/imported/optimized_coding_standards.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": ".augment/rules/imported/optimized_coding_standards.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/.gitignore": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": ".giti<PERSON>re"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/.nextest.toml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": ".nextest.toml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/.pre-commit-config.yaml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": ".pre-commit-config.yaml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/CI-CD.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "CI-CD.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/DOCKER.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "DOCKER.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/Dockerfile": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "Dockerfile"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/Dockerfile.dev-tools": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "Dockerfile.dev-tools"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/Leptos.toml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "Leptos.toml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/PROJECT_SUMMARY.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "PROJECT_SUMMARY.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/.env.example": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": ".env.example"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/Cargo.lock": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "Cargo.lock"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/Cargo.toml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "Cargo.toml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/Dockerfile.dev": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "Dockerfile.dev"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/clippy.toml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "clippy.toml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/deny.toml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "deny.toml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/docker-compose.dev.yml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "docker-compose.dev.yml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/docker-compose.prod.yml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "docker-compose.prod.yml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/progress.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "progress.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/rust-toolchain.toml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "rust-toolchain.toml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/rustfmt.toml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "rustfmt.toml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/tarpaulin.toml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "tarpaulin.toml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/vite.config.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "vite.config.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/wasm-crypto/Cargo.toml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "wasm-crypto/Cargo.toml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/wasm-crypto/build.sh": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "wasm-crypto/build.sh"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/wasm-crypto/test.html": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "wasm-crypto/test.html"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/wasm-crypto/src/crypto.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "wasm-crypto/src/crypto.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/wasm-crypto/src/key_derivation.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "wasm-crypto/src/key_derivation.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/wasm-crypto/src/lib.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "wasm-crypto/src/lib.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/wasm-crypto/src/types.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "wasm-crypto/src/types.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/wasm-crypto/src/utils.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "wasm-crypto/src/utils.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/style/tailwind.css": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "style/tailwind.css"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/security_results/security_audit_report.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "security_results/security_audit_report.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/scripts/code-quality.sh": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "scripts/code-quality.sh"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/scripts/dev-setup.sh": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "scripts/dev-setup.sh"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/scripts/load_test.py": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "scripts/load_test.py"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/main.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/main.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/types/metadata.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/types/metadata.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/auth/client.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/auth/client.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/performance_results/performance_report.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "performance_results/performance_report.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/dist/node/index.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/dist/node/index.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/dist/node/chunks/dep-CxRDE86e.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/dist/node/chunks/dep-CxRDE86e.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/shared/parseAst.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/shared/parseAst.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/auth/zk_auth.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/auth/zk_auth.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/dist/node/chunks/dep-Bh-1mzXX.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/dist/node/chunks/dep-Bh-1mzXX.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/Cargo.toml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/Cargo.toml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/loadConfigFile.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/loadConfigFile.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/components/mod.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/components/mod.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/performance_results/rust_benchmarks.txt": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "performance_results/rust_benchmarks.txt"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/auth/session.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/auth/session.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/style/main.scss": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/style/main.scss"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/picomatch/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/picomatch/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/es/rollup.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/es/rollup.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/shared/watch-cli.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/shared/watch-cli.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/tailwind.config.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/tailwind.config.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/dist/client/client.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/dist/client/client.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/components/icons.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/components/icons.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/AliasFieldPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/AliasFieldPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/LogInfoPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/LogInfoPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/picomatch/lib/constants.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/picomatch/lib/constants.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/dist/node/chunks/dep-f94b-k0u.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/dist/node/chunks/dep-f94b-k0u.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/lightningcss-darwin-arm64/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/lightningcss-darwin-arm64/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/crypto/serialization.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/crypto/serialization.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/shared/loadConfigFile.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/shared/loadConfigFile.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/components/register.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/components/register.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/style/template-sidebar-animations.css": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/style/template-sidebar-animations.css"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/dist/node/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/dist/node/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/es/shared/watch.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/es/shared/watch.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/performance_results/integration_test_results.txt": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "performance_results/integration_test_results.txt"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/monitoring/promtail/promtail-config.yml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/promtail/promtail-config.yml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/crypto/types.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/crypto/types.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/auth/storage.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/auth/storage.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/components/zk_register.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/components/zk_register.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/auth/types.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/auth/types.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/dist/node/chunks/dep-G9cgWHwY.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/dist/node/chunks/dep-G9cgWHwY.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/performance/load-test.yml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "performance/load-test.yml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/parseAst.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/parseAst.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/dist/node/chunks/dep-BDCsDwBr.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/dist/node/chunks/dep-BDCsDwBr.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/components/zk_demo.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/components/zk_demo.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/auth/mod.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/auth/mod.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/dist/node/constants.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/dist/node/constants.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/es/getLogFilter.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/es/getLogFilter.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/jiti/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/jiti/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/jiti/dist/jiti.cjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/jiti/dist/jiti.cjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/package-lock.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/package-lock.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/monitoring/grafana/dashboards/excelsync-dashboard.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/grafana/dashboards/excelsync-dashboard.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/crypto/random.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/crypto/random.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/ImportsFieldPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/ImportsFieldPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/dist/node/chunks/dep-Bq0QZiuR.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/dist/node/chunks/dep-Bq0QZiuR.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/dist/node/chunks/dep-V5uAjiuB.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/dist/node/chunks/dep-V5uAjiuB.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/components/demo_page.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/components/demo_page.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/monitoring/alertmanager/alertmanager.yml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/alertmanager/alertmanager.yml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/dist/node/module-runner.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/dist/node/module-runner.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/rollup.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/rollup.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/monitoring/loki/loki-config.yml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/loki/loki-config.yml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/es/parseAst.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/es/parseAst.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/components/excel_animation.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/components/excel_animation.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/monitoring/docker-compose.yml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/docker-compose.yml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/jiti/lib/jiti-hooks.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/jiti/lib/jiti-hooks.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/shared/fsevents-importer.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/shared/fsevents-importer.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/bin/openChrome.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/bin/openChrome.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/shared/watch.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/shared/watch.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/TEMPLATE_SIDEBAR_ANIMATION.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/TEMPLATE_SIDEBAR_ANIMATION.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/ExportsFieldPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/ExportsFieldPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/components/webauthn_auth.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/components/webauthn_auth.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/crypto/wasm_integration.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/crypto/wasm_integration.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/crypto/kdf.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/crypto/kdf.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/auth/webauthn.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/auth/webauthn.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/dist/node/chunks/dep-BeJ5EQ78.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/dist/node/chunks/dep-BeJ5EQ78.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/lightningcss-darwin-arm64/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/lightningcss-darwin-arm64/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/bin/rollup": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/bin/rollup"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/components/template_sidebar_animation.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/components/template_sidebar_animation.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/lightningcss-darwin-arm64/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/lightningcss-darwin-arm64/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/crypto/aead.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/crypto/aead.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/dist/node/module-runner.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/dist/node/module-runner.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/dist/node/chunks/dep-By9Vn5UD.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/dist/node/chunks/dep-By9Vn5UD.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/picomatch/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/picomatch/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/lib.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/lib.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/jiti/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/jiti/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/jiti/lib/types.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/jiti/lib/types.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/monitoring/grafana/datasources/prometheus.yml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/grafana/datasources/prometheus.yml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/performance_results/unit_test_results.txt": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "performance_results/unit_test_results.txt"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/native.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/native.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/shared/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/shared/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/.gitignore": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/.gitignore"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/es/shared/parseAst.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/es/shared/parseAst.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/monitoring/prometheus/prometheus.yml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/prometheus/prometheus.yml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/monitoring/grafana/dashboards/dashboard.yml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/grafana/dashboards/dashboard.yml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/crypto/mod.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/crypto/mod.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/monitoring/production-config.yml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/production-config.yml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/components/zk_login.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/components/zk_login.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/getLogFilter.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/getLogFilter.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/components/login.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/components/login.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/monitoring/prometheus/alert_rules.yml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/prometheus/alert_rules.yml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/scripts/run_performance_tests.sh": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "scripts/run_performance_tests.sh"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/dist/node/cli.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/dist/node/cli.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/monitoring/setup-monitoring.sh": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/setup-monitoring.sh"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/.package-lock.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/.package-lock.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/LICENSE.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/LICENSE.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/app.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/app.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/SymlinkPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/SymlinkPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/util/memoize.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/util/memoize.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/util/path.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/util/path.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/vite/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/vite/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/vite/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/vite/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/oxide-darwin-arm64/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/oxide-darwin-arm64/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/oxide-darwin-arm64/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/oxide-darwin-arm64/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@esbuild/darwin-arm64/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@esbuild/darwin-arm64/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/end2end/package-lock.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/end2end/package-lock.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/end2end/playwright.config.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/end2end/playwright.config.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/performance/query_optimizer.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/performance/query_optimizer.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/Cargo.toml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/Cargo.toml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/tests/integration_tests.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/tests/integration_tests.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/tests/security_tests.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/tests/security_tests.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/lib.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/lib.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/state.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/state.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/services/audit_service.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/audit_service.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/services/backup_scheduler.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/backup_scheduler.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/services/file_service.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/file_service.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/services/financial_calculator.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/financial_calculator.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/services/financial_service.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/financial_service.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/services/health_service.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/health_service.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/services/mod.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/mod.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/services/organization_service.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/organization_service.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/services/project_service.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/project_service.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/services/report_service.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/report_service.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/services/template_service.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/template_service.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/services/user_service.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/user_service.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/services/validation_service.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/validation_service.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/services/versioning_service.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/versioning_service.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/services/vietnamese_tax.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/vietnamese_tax.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/services/websocket_service.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/websocket_service.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/types.d.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/types.d.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/AliasPlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/AliasPlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/CachedInputFileSystem.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/CachedInputFileSystem.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/DescriptionFilePlugin.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/DescriptionFilePlugin.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/DescriptionFileUtils.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/DescriptionFileUtils.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/Resolver.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/Resolver.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/ResolverFactory.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/ResolverFactory.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/index.js": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/index.js"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/vite/LICENSE": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/vite/LICENSE"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/vite/dist/index.d.mts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/vite/dist/index.d.mts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/vite/dist/index.mjs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/vite/dist/index.mjs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/oxide-darwin-arm64/README.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/oxide-darwin-arm64/README.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/end2end/package.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/end2end/package.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/end2end/tsconfig.json": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/end2end/tsconfig.json"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/end2end/tests/example.spec.ts": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/end2end/tests/example.spec.ts"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/docs/API.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "docs/API.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/docs/DEVELOPMENT.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "docs/DEVELOPMENT.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/Cargo.toml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/Cargo.toml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/backup.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/backup.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/connection.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/connection.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/repositories/base.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/repositories/base.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/repositories/mod.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/repositories/mod.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/repositories/organization_repository.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/repositories/organization_repository.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/repositories/project_repository.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/repositories/project_repository.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/repositories/session_repository.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/repositories/session_repository.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/repositories/template_repository.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/repositories/template_repository.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/repositories/user_repository.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/repositories/user_repository.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/performance/cache.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/performance/cache.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/performance/connection_pool.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/performance/connection_pool.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/performance/metrics.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/performance/metrics.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/performance/mod.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/performance/mod.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/migrations/m20250106_000001_create_organizations.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/migrations/m20250106_000001_create_organizations.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/migrations/m20250106_000002_create_users.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/migrations/m20250106_000002_create_users.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/migrations/m20250106_000003_create_projects.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/migrations/m20250106_000003_create_projects.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/migrations/m20250106_000004_create_templates.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/migrations/m20250106_000004_create_templates.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/migrations/m20250106_000005_create_project_data.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/migrations/m20250106_000005_create_project_data.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/migrations/m20250106_000006_create_sessions.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/migrations/m20250106_000006_create_sessions.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/migrations/m20250106_000007_create_audit_logs.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/migrations/m20250106_000007_create_audit_logs.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/migrations/m20250106_000008_create_indexes.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/migrations/m20250106_000008_create_indexes.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/migrations/m20250108_000001_create_financial_calculations.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/migrations/m20250108_000001_create_financial_calculations.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/migrations/m20250108_000002_create_data_versions.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/migrations/m20250108_000002_create_data_versions.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/migrations/mod.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/migrations/mod.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/entities/audit_logs.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/audit_logs.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/entities/data_versions.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/data_versions.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/entities/financial_calculations.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/financial_calculations.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/entities/mod.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/mod.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/entities/organizations.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/organizations.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/entities/project_data.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/project_data.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/entities/projects.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/projects.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/entities/sessions.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/sessions.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/entities/templates.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/templates.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/entities/users.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/users.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/core/Cargo.toml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "core/Cargo.toml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/core/src/config.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "core/src/config.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/core/src/encryption.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "core/src/encryption.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/core/src/error.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "core/src/error.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/core/src/lib.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "core/src/lib.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/core/src/performance/caching.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "core/src/performance/caching.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/core/src/performance/memory_manager.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "core/src/performance/memory_manager.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/core/src/performance/mod.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "core/src/performance/mod.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/core/src/performance/response_optimizer.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "core/src/performance/response_optimizer.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/core/src/performance/scaling.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "core/src/performance/scaling.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/config/default.toml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "config/default.toml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/backend/Cargo.toml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "backend/Cargo.toml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/backend/src/main.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "backend/src/main.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/backend/src/bin/data_fetcher.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "backend/src/bin/data_fetcher.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/backend/src/bin/simple_db_test.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "backend/src/bin/simple_db_test.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/auth/Cargo.toml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "auth/Cargo.toml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/auth/src/jwt.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "auth/src/jwt.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/auth/src/lib.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "auth/src/lib.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/auth/src/password.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "auth/src/password.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/auth/src/session.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "auth/src/session.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/services/zk_crypto_service.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/zk_crypto_service.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/routes/audit.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/audit.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/routes/auth.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/auth.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/routes/backup.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/backup.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/routes/files.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/files.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/routes/financial.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/financial.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/routes/health.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/health.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/routes/mod.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/mod.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/routes/organizations.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/organizations.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/routes/projects.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/projects.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/routes/reports.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/reports.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/routes/templates.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/templates.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/routes/users.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/users.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/routes/validation.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/validation.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/routes/versioning.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/versioning.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/routes/websocket.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/websocket.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/routes/zk_auth.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/zk_auth.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/routes/zk_data.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/zk_data.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/middleware/auth.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/middleware/auth.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/middleware/cors.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/middleware/cors.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/middleware/logging.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/middleware/logging.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/middleware/mod.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/middleware/mod.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/middleware/rate_limit.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/middleware/rate_limit.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/middleware/validation.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/middleware/validation.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/metrics/mod.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/metrics/mod.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/handlers/error.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/handlers/error.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/handlers/mod.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/handlers/mod.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/handlers/response.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/handlers/response.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/dto/audit.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/audit.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/dto/auth.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/auth.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/dto/common.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/common.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/dto/financial.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/financial.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/dto/mod.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/mod.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/dto/organization.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/organization.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/dto/project.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/project.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/dto/reports.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/reports.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/dto/template.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/template.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/dto/user.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/user.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/dto/validation.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/validation.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/dto/versioning.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/versioning.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/dto/zk_auth.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/zk_auth.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/benches/performance_benchmarks.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/benches/performance_benchmarks.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/.github/workflows/ci.yml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": ".github/workflows/ci.yml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/.github/workflows/deploy.yml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": ".github/workflows/deploy.yml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/.github/workflows/security.yml": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": ".github/workflows/security.yml"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/.augment/rules/imported/LLVM_Developer_Policy.md": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": ".augment/rules/imported/LLVM_Developer_Policy.md"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/lib.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/lib.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/services/mod.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/services/mod.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/services/data_fetcher.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/services/data_fetcher.rs"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/repositories/audit_repository.rs": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/repositories/audit_repository.rs"}}