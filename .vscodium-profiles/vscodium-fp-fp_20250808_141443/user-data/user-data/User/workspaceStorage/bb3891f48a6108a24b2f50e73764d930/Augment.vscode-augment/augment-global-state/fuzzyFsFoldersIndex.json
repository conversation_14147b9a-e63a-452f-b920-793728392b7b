{"/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/yallist/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/yallist/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/yallist/dist/esm/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/yallist/dist/esm/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/yallist/dist/commonjs/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/yallist/dist/commonjs/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/types/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/types/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/types/internal/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/types/internal/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/misc/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/misc/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/dist/node/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/dist/node/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/dist/node/chunks/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/dist/node/chunks/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/dist/client/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/dist/client/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/vite/bin/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/vite/bin/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tinyglobby/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tinyglobby/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tinyglobby/dist/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tinyglobby/dist/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/esm/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/esm/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tar/dist/commonjs/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tar/dist/commonjs/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tapable/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tapable/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tapable/lib/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tapable/lib/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/tailwindcss/dist/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/tailwindcss/dist/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/source-map-js/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/source-map-js/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/source-map-js/lib/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/source-map-js/lib/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/es/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/es/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/postcss/lib/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/postcss/lib/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/picomatch/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/picomatch/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/picomatch/lib/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/picomatch/lib/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/picocolors/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/picocolors/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/url-alphabet/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/url-alphabet/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/non-secure/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/non-secure/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/bin/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/bin/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/nanoid/async/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/nanoid/async/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/mjs/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/mjs/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/mkdirp/dist/cjs/src/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/mkdirp/dist/cjs/src/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/dist/esm/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/dist/esm/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minizlib/dist/commonjs/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minizlib/dist/commonjs/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minipass/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minipass/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minipass/dist/esm/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minipass/dist/esm/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/minipass/dist/commonjs/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/minipass/dist/commonjs/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/magic-string/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/magic-string/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/magic-string/dist/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/magic-string/dist/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/lightningcss/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/lightningcss/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/lightningcss/node/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/lightningcss/node/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/jiti/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/jiti/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/jiti/lib/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/jiti/lib/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/graceful-fs/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/graceful-fs/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/builder/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/builder/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/fdir/dist/api/functions/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/fdir/dist/api/functions/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/esbuild/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/esbuild/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/esbuild/lib/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/esbuild/lib/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/enhanced-resolve/lib/util/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/enhanced-resolve/lib/util/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/detect-libc/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/detect-libc/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/detect-libc/lib/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/detect-libc/lib/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/chownr/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/chownr/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/chownr/dist/esm/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/chownr/dist/esm/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/chownr/dist/commonjs/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/chownr/dist/commonjs/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@types/estree/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@types/estree/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/oxide/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/oxide/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/oxide/scripts/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/oxide/scripts/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/node/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/node/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/node/dist/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/node/dist/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@rollup/rollup-darwin-arm64/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@rollup/rollup-darwin-arm64/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/types/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/types/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/src/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/src/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/trace-mapping/dist/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/trace-mapping/dist/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/types/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/types/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/src/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/src/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/sourcemap-codec/dist/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/sourcemap-codec/dist/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/resolve-uri/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/resolve-uri/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/resolve-uri/dist/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/resolve-uri/dist/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/resolve-uri/dist/types/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/resolve-uri/dist/types/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/types/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/types/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/src/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/src/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@jridgewell/gen-mapping/dist/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@jridgewell/gen-mapping/dist/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@isaacs/fs-minipass/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@isaacs/fs-minipass/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@isaacs/fs-minipass/dist/esm/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@isaacs/fs-minipass/dist/esm/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@isaacs/fs-minipass/dist/commonjs/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@isaacs/fs-minipass/dist/commonjs/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@esbuild/darwin-arm64/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@esbuild/darwin-arm64/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@ampproject/remapping/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@ampproject/remapping/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@ampproject/remapping/dist/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@ampproject/remapping/dist/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@ampproject/remapping/dist/types/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@ampproject/remapping/dist/types/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/.augment/rules/imported/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": ".augment/rules/imported/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": ""}, "/Users/<USER>/Documents/augment-projects/ExcelSync/wasm-crypto/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "wasm-crypto/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/wasm-crypto/src/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "wasm-crypto/src/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/style/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "style/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/security_results/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "security_results/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/scripts/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "scripts/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/auth/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/auth/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/performance_results/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "performance_results/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/shared/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/shared/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/components/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/components/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/style/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/style/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/lightningcss-darwin-arm64/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/lightningcss-darwin-arm64/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/src/crypto/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/src/crypto/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/es/shared/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/es/shared/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/monitoring/promtail/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/promtail/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/performance/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "performance/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/jiti/dist/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/jiti/dist/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/monitoring/grafana/dashboards/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/grafana/dashboards/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/monitoring/alertmanager/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/alertmanager/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/monitoring/loki/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/loki/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/monitoring/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/rollup/dist/bin/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/rollup/dist/bin/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/monitoring/grafana/datasources/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/grafana/datasources/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/monitoring/prometheus/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "monitoring/prometheus/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/vite/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/vite/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/oxide-darwin-arm64/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/oxide-darwin-arm64/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/end2end/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/end2end/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/performance/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/performance/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/tests/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/tests/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/services/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/services/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/node_modules/@tailwindcss/vite/dist/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/node_modules/@tailwindcss/vite/dist/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/frontend/end2end/tests/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "frontend/end2end/tests/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/docs/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "docs/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/repositories/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/repositories/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/migrations/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/migrations/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/entities/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/entities/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/core/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "core/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/core/src/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "core/src/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/core/src/performance/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "core/src/performance/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/config/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "config/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/backend/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "backend/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/backend/src/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "backend/src/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/backend/src/bin/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "backend/src/bin/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/auth/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "auth/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/auth/src/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "auth/src/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/routes/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/routes/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/middleware/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/middleware/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/metrics/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/metrics/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/handlers/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/handlers/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/src/dto/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/src/dto/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/api/benches/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "api/benches/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/.github/workflows/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": ".github/workflows/"}, "/Users/<USER>/Documents/augment-projects/ExcelSync/database/src/services/": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/services/"}}