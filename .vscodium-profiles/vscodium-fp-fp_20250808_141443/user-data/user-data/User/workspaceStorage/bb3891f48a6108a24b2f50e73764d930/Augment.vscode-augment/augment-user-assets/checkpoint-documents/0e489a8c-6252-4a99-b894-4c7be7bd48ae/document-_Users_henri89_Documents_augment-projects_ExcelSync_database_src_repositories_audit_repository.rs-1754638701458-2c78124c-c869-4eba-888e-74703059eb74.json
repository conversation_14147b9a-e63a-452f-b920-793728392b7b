{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/ExcelSync", "relPath": "database/src/repositories/audit_repository.rs"}, "originalCode": "use super::base::BaseRepository;\nuse super::{Repository, RepositoryError};\nuse crate::entities::audit_logs;\nuse anyhow::Result;\nuse async_trait::async_trait;\nuse sea_orm::{ColumnTrait, DatabaseConnection, DbErr, QueryFilter, Order, EntityTrait};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\n\n/// Audit repository for managing audit log entities\npub struct AuditRepository {\n    base: BaseRepository<audit_logs::Entity, audit_logs::Model, audit_logs::ActiveModel>,\n}\n\nimpl AuditRepository {\n    pub fn new(db: DatabaseConnection) -> Self {\n        Self {\n            base: BaseRepository::new(db),\n        }\n    }\n\n    /// Find audit logs by user ID\n    pub async fn find_by_user(&self, user_id: Uuid) -> Result<Vec<audit_logs::Model>, DbErr> {\n        self.base\n            .find_by_condition(audit_logs::Column::UserId.eq(user_id))\n            .await\n    }\n\n    /// Find audit logs by entity\n    pub async fn find_by_entity(&self, entity_type: &str, entity_id: Uuid) -> Result<Vec<audit_logs::Model>, DbErr> {\n        audit_logs::Entity::find()\n            .filter(audit_logs::Column::EntityType.eq(entity_type))\n            .filter(audit_logs::Column::EntityId.eq(entity_id))\n            .order_by_desc(audit_logs::Column::CreatedAt)\n            .all(self.base.db())\n            .await\n    }\n\n    /// Find audit logs by action\n    pub async fn find_by_action(&self, action: &str) -> Result<Vec<audit_logs::Model>, DbErr> {\n        self.base\n            .find_by_condition(audit_logs::Column::Action.eq(action))\n            .await\n    }\n\n    /// Find audit logs within date range\n    pub async fn find_by_date_range(\n        &self,\n        start_date: DateTime<Utc>,\n        end_date: DateTime<Utc>,\n    ) -> Result<Vec<audit_logs::Model>, DbErr> {\n        audit_logs::Entity::find()\n            .filter(audit_logs::Column::CreatedAt.gte(start_date))\n            .filter(audit_logs::Column::CreatedAt.lte(end_date))\n            .order_by_desc(audit_logs::Column::CreatedAt)\n            .all(self.base.db())\n            .await\n    }\n\n    /// Get recent audit logs\n    pub async fn find_recent(&self, limit: u64) -> Result<Vec<audit_logs::Model>, DbErr> {\n        audit_logs::Entity::find()\n            .order_by_desc(audit_logs::Column::CreatedAt)\n            .limit(limit)\n            .all(self.base.db())\n            .await\n    }\n\n    /// Clean up old audit logs (older than specified days)\n    pub async fn cleanup_old_logs(&self, days: i64) -> Result<u64, DbErr> {\n        let cutoff_date = Utc::now() - chrono::Duration::days(days);\n        self.base\n            .delete_by_condition(audit_logs::Column::CreatedAt.lt(cutoff_date))\n            .await\n    }\n}\n\n#[async_trait]\nimpl Repository<audit_logs::Model, Uuid> for AuditRepository {\n    async fn find_by_id(&self, id: Uuid) -> Result<Option<audit_logs::Model>, DbErr> {\n        self.base.find_by_pk(id).await\n    }\n\n    async fn find_all(&self, limit: Option<u64>, offset: Option<u64>) -> Result<Vec<audit_logs::Model>, DbErr> {\n        match (limit, offset) {\n            (Some(l), Some(o)) => self.base.find_with_limit(l, o).await,\n            _ => self.base.find_all().await,\n        }\n    }\n\n    async fn create(&self, entity: audit_logs::Model) -> Result<audit_logs::Model, DbErr> {\n        let active_model: audit_logs::ActiveModel = entity.into();\n        self.base.create(active_model).await\n    }\n\n    async fn update(&self, entity: audit_logs::Model) -> Result<audit_logs::Model, DbErr> {\n        let active_model: audit_logs::ActiveModel = entity.into();\n        self.base.update(active_model).await\n    }\n\n    async fn delete(&self, id: Uuid) -> Result<bool, DbErr> {\n        self.base.delete_by_pk(id).await\n    }\n\n    async fn count(&self) -> Result<u64, DbErr> {\n        self.base.count().await\n    }\n\n    async fn exists(&self, id: Uuid) -> Result<bool, DbErr> {\n        self.base.exists_by_pk(id).await\n    }\n}\n", "modifiedCode": "use super::base::BaseRepository;\nuse super::{Repository, RepositoryError};\nuse crate::entities::audit_logs;\nuse anyhow::Result;\nuse async_trait::async_trait;\nuse sea_orm::{ColumnTrait, DatabaseConnection, DbErr, QueryFilter, QueryOrder, Order, EntityTrait, Condition};\nuse uuid::Uuid;\nuse chrono::{DateTime, Utc};\n\n/// Audit repository for managing audit log entities\npub struct AuditRepository {\n    base: BaseRepository<audit_logs::Entity, audit_logs::Model, audit_logs::ActiveModel>,\n}\n\nimpl AuditRepository {\n    pub fn new(db: DatabaseConnection) -> Self {\n        Self {\n            base: BaseRepository::new(db),\n        }\n    }\n\n    /// Find audit logs by user ID\n    pub async fn find_by_user(&self, user_id: Uuid) -> Result<Vec<audit_logs::Model>, DbErr> {\n        self.base\n            .find_by_condition(audit_logs::Column::UserId.eq(user_id))\n            .await\n    }\n\n    /// Find audit logs by entity\n    pub async fn find_by_entity(&self, entity_type: &str, entity_id: Uuid) -> Result<Vec<audit_logs::Model>, DbErr> {\n        audit_logs::Entity::find()\n            .filter(audit_logs::Column::EntityType.eq(entity_type))\n            .filter(audit_logs::Column::EntityId.eq(entity_id))\n            .order_by_desc(audit_logs::Column::CreatedAt)\n            .all(self.base.db())\n            .await\n    }\n\n    /// Find audit logs by action\n    pub async fn find_by_action(&self, action: &str) -> Result<Vec<audit_logs::Model>, DbErr> {\n        self.base\n            .find_by_condition(audit_logs::Column::Action.eq(action))\n            .await\n    }\n\n    /// Find audit logs within date range\n    pub async fn find_by_date_range(\n        &self,\n        start_date: DateTime<Utc>,\n        end_date: DateTime<Utc>,\n    ) -> Result<Vec<audit_logs::Model>, DbErr> {\n        audit_logs::Entity::find()\n            .filter(audit_logs::Column::CreatedAt.gte(start_date))\n            .filter(audit_logs::Column::CreatedAt.lte(end_date))\n            .order_by_desc(audit_logs::Column::CreatedAt)\n            .all(self.base.db())\n            .await\n    }\n\n    /// Get recent audit logs\n    pub async fn find_recent(&self, limit: u64) -> Result<Vec<audit_logs::Model>, DbErr> {\n        audit_logs::Entity::find()\n            .order_by_desc(audit_logs::Column::CreatedAt)\n            .limit(limit)\n            .all(self.base.db())\n            .await\n    }\n\n    /// Clean up old audit logs (older than specified days)\n    pub async fn cleanup_old_logs(&self, days: i64) -> Result<u64, DbErr> {\n        let cutoff_date = Utc::now() - chrono::Duration::days(days);\n        self.base\n            .delete_by_condition(audit_logs::Column::CreatedAt.lt(cutoff_date))\n            .await\n    }\n}\n\n#[async_trait]\nimpl Repository<audit_logs::Model, Uuid> for AuditRepository {\n    async fn find_by_id(&self, id: Uuid) -> Result<Option<audit_logs::Model>, DbErr> {\n        self.base.find_by_pk(id).await\n    }\n\n    async fn find_all(&self, limit: Option<u64>, offset: Option<u64>) -> Result<Vec<audit_logs::Model>, DbErr> {\n        match (limit, offset) {\n            (Some(l), Some(o)) => self.base.find_with_limit(l, o).await,\n            _ => self.base.find_all().await,\n        }\n    }\n\n    async fn create(&self, entity: audit_logs::Model) -> Result<audit_logs::Model, DbErr> {\n        let active_model: audit_logs::ActiveModel = entity.into();\n        self.base.create(active_model).await\n    }\n\n    async fn update(&self, entity: audit_logs::Model) -> Result<audit_logs::Model, DbErr> {\n        let active_model: audit_logs::ActiveModel = entity.into();\n        self.base.update(active_model).await\n    }\n\n    async fn delete(&self, id: Uuid) -> Result<bool, DbErr> {\n        self.base.delete_by_pk(id).await\n    }\n\n    async fn count(&self) -> Result<u64, DbErr> {\n        self.base.count().await\n    }\n\n    async fn exists(&self, id: Uuid) -> Result<bool, DbErr> {\n        self.base.exists_by_pk(id).await\n    }\n}\n"}