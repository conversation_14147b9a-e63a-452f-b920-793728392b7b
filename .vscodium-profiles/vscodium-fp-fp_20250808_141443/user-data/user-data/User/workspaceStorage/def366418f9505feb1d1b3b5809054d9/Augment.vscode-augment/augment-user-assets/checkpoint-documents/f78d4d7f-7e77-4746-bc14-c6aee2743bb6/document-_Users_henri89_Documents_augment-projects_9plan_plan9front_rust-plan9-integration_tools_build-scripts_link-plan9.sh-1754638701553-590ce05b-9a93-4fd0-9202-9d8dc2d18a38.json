{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/9plan/plan9front", "relPath": "rust-plan9-integration/tools/build-scripts/link-plan9.sh"}, "modifiedCode": "#!/bin/bash\n# Plan 9 Linking Script for Rust Programs\n# This script helps link Rust .rlib files with Plan 9's native linkers\n\nset -e\n\n# Colors for output\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nNC='\\033[0m' # No Color\n\n# Configuration\nPROJECT_ROOT=\"$(cd \"$(dirname \"${BASH_SOURCE[0]}\")/../..\" && pwd)\"\nTARGET_DIR=\"$PROJECT_ROOT/target\"\n\nprint_header() {\n    echo -e \"${BLUE}================================${NC}\"\n    echo -e \"${BLUE}  Plan 9 Rust Linker${NC}\"\n    echo -e \"${BLUE}================================${NC}\"\n    echo\n}\n\nprint_success() {\n    echo -e \"${GREEN}✓ $1${NC}\"\n}\n\nprint_error() {\n    echo -e \"${RED}✗ $1${NC}\"\n}\n\nprint_warning() {\n    echo -e \"${YELLOW}⚠ $1${NC}\"\n}\n\nprint_info() {\n    echo -e \"${BLUE}ℹ $1${NC}\"\n}\n\nshow_usage() {\n    echo \"Usage: $0 [OPTIONS] <target> <program>\"\n    echo\n    echo \"TARGETS:\"\n    echo \"  x86_64-unknown-plan9    64-bit Plan 9 (use 6l linker)\"\n    echo \"  i386-unknown-plan9      32-bit Plan 9 (use 8l linker)\"\n    echo\n    echo \"PROGRAMS:\"\n    echo \"  hello                   Basic hello world program\"\n    echo \"  integration             Integration test program\"\n    echo \"  fileio                  File I/O example\"\n    echo \"  process                 Process management example\"\n    echo\n    echo \"OPTIONS:\"\n    echo \"  -h, --help             Show this help message\"\n    echo \"  -o, --output FILE      Output executable name (default: program name)\"\n    echo \"  -v, --verbose          Verbose output\"\n    echo\n    echo \"EXAMPLES:\"\n    echo \"  $0 x86_64-unknown-plan9 hello\"\n    echo \"  $0 i386-unknown-plan9 integration -o test_program\"\n    echo\n    echo \"NOTE: This script generates Plan 9 assembly and linking instructions.\"\n    echo \"      Actual linking must be done within a Plan 9 environment.\"\n}\n\nget_linker_for_target() {\n    local target=$1\n    case $target in\n        x86_64-unknown-plan9)\n            echo \"6l\"\n            ;;\n        i386-unknown-plan9)\n            echo \"8l\"\n            ;;\n        *)\n            echo \"unknown\"\n            ;;\n    esac\n}\n\nget_assembler_for_target() {\n    local target=$1\n    case $target in\n        x86_64-unknown-plan9)\n            echo \"6a\"\n            ;;\n        i386-unknown-plan9)\n            echo \"8a\"\n            ;;\n        *)\n            echo \"unknown\"\n            ;;\n    esac\n}\n\ngenerate_startup_code() {\n    local target=$1\n    local output_file=$2\n    \n    print_info \"Generating Plan 9 startup code for $target\"\n    \n    case $target in\n        x86_64-unknown-plan9)\n            cat > \"$output_file\" << 'EOF'\n// Plan 9 x86_64 startup code for Rust programs\nTEXT _main(SB), 1, $-8\n    MOVQ    $runtime_main(SB), AX\n    CALL    AX\n    MOVQ    $0, AX\n    CALL    exits(SB)\n    RET\n\n// Rust main function (to be linked from Rust code)\nTEXT runtime_main(SB), 1, $0\n    // This will be provided by the Rust runtime\n    RET\n\n// Plan 9 system call stubs\nTEXT exits(SB), 1, $0\n    MOVQ    $8, AX      // exits system call number\n    SYSCALL\n    RET\n\nTEXT open(SB), 1, $0\n    MOVQ    $2, AX      // open system call number  \n    SYSCALL\n    RET\n\nTEXT read(SB), 1, $0\n    MOVQ    $4, AX      // read system call number\n    SYSCALL\n    RET\n\nTEXT write(SB), 1, $0\n    MOVQ    $51, AX     // pwrite system call number (Plan 9 write)\n    SYSCALL\n    RET\n\nTEXT close(SB), 1, $0\n    MOVQ    $4, AX      // close system call number\n    SYSCALL\n    RET\nEOF\n            ;;\n        i386-unknown-plan9)\n            cat > \"$output_file\" << 'EOF'\n// Plan 9 i386 startup code for Rust programs  \nTEXT _main(SB), 1, $0\n    CALL    runtime_main(SB)\n    PUSHL   $0\n    CALL    exits(SB)\n    RET\n\n// Rust main function (to be linked from Rust code)\nTEXT runtime_main(SB), 1, $0\n    // This will be provided by the Rust runtime\n    RET\n\n// Plan 9 system call stubs\nTEXT exits(SB), 1, $0\n    MOVL    $8, AX      // exits system call number\n    INT     $0x40\n    RET\n\nTEXT open(SB), 1, $0\n    MOVL    $2, AX      // open system call number\n    INT     $0x40\n    RET\n\nTEXT read(SB), 1, $0\n    MOVL    $4, AX      // read system call number\n    INT     $0x40\n    RET\n\nTEXT write(SB), 1, $0\n    MOVL    $51, AX     // pwrite system call number\n    INT     $0x40\n    RET\n\nTEXT close(SB), 1, $0\n    MOVL    $4, AX      // close system call number\n    INT     $0x40\n    RET\nEOF\n            ;;\n    esac\n}\n\ngenerate_link_script() {\n    local target=$1\n    local program=$2\n    local output=$3\n    local linker=$(get_linker_for_target \"$target\")\n    local assembler=$(get_assembler_for_target \"$target\")\n    local startup_asm=\"startup_${target}.s\"\n    local startup_obj=\"startup_${target}.6\"\n    \n    if [ \"$target\" = \"i386-unknown-plan9\" ]; then\n        startup_obj=\"startup_${target}.8\"\n    fi\n    \n    print_info \"Generating linking script for $program on $target\"\n    \n    cat > \"link_${program}_${target}.sh\" << EOF\n#!/bin/rc\n# Plan 9 linking script for $program on $target\n# Generated by Rust Plan 9 integration tools\n\necho 'Linking $program for $target...'\n\n# Assemble startup code\n$assembler $startup_asm\n\n# Link with Rust libraries\n# Note: You'll need to extract object files from .rlib archives first\n# ar x libplan9_runtime.rlib\n# ar x libcore.rlib  \n# ar x liballoc.rlib\n# ar x libcompiler_builtins.rlib\n\n# Link everything together\n$linker -o $output $startup_obj *.6\n\necho 'Linking complete: $output'\nEOF\n    \n    chmod +x \"link_${program}_${target}.sh\"\n    print_success \"Created linking script: link_${program}_${target}.sh\"\n}\n\nmain() {\n    local target=\"\"\n    local program=\"\"\n    local output=\"\"\n    local verbose=false\n    \n    # Parse command line arguments\n    while [[ $# -gt 0 ]]; do\n        case $1 in\n            -h|--help)\n                show_usage\n                exit 0\n                ;;\n            -o|--output)\n                output=\"$2\"\n                shift 2\n                ;;\n            -v|--verbose)\n                verbose=true\n                shift\n                ;;\n            x86_64-unknown-plan9|i386-unknown-plan9)\n                target=\"$1\"\n                shift\n                ;;\n            hello|integration|fileio|process)\n                program=\"$1\"\n                shift\n                ;;\n            *)\n                print_error \"Unknown option: $1\"\n                show_usage\n                exit 1\n                ;;\n        esac\n    done\n    \n    # Validate arguments\n    if [ -z \"$target\" ]; then\n        print_error \"Target architecture required\"\n        show_usage\n        exit 1\n    fi\n    \n    if [ -z \"$program\" ]; then\n        print_error \"Program name required\"\n        show_usage\n        exit 1\n    fi\n    \n    if [ -z \"$output\" ]; then\n        output=\"$program\"\n    fi\n    \n    print_header\n    \n    # Check if target libraries exist\n    local target_dir=\"$TARGET_DIR/$target/release\"\n    if [ ! -d \"$target_dir\" ]; then\n        print_error \"Target directory not found: $target_dir\"\n        print_info \"Run cross-compilation first: ./tools/build-scripts/cross-compile-plan9.sh\"\n        exit 1\n    fi\n    \n    # Generate startup code\n    local startup_file=\"startup_${target}.s\"\n    generate_startup_code \"$target\" \"$startup_file\"\n    print_success \"Generated startup code: $startup_file\"\n    \n    # Generate linking script\n    generate_link_script \"$target\" \"$program\" \"$output\"\n    \n    # Create transfer package\n    local package_dir=\"plan9_link_${program}_${target}\"\n    mkdir -p \"$package_dir\"\n    \n    # Copy necessary files\n    cp \"$startup_file\" \"$package_dir/\"\n    cp \"link_${program}_${target}.sh\" \"$package_dir/\"\n    \n    # Copy Rust libraries\n    if [ -f \"$target_dir/libplan9_runtime.rlib\" ]; then\n        cp \"$target_dir/libplan9_runtime.rlib\" \"$package_dir/\"\n        print_success \"Copied runtime library\"\n    fi\n    \n    # Copy dependencies\n    find \"$target_dir/deps\" -name \"*.rlib\" -exec cp {} \"$package_dir/\" \\; 2>/dev/null || true\n    \n    # Create instructions\n    cat > \"$package_dir/README.md\" << EOF\n# Plan 9 Linking Instructions for $program\n\n## Files in this package:\n- \\`$startup_file\\` - Plan 9 assembly startup code\n- \\`link_${program}_${target}.sh\\` - Plan 9 linking script\n- \\`*.rlib\\` - Rust library files\n\n## Steps to link in Plan 9:\n\n1. Transfer this entire directory to your Plan 9 system\n2. Extract object files from Rust libraries:\n   \\`\\`\\`\n   for lib in *.rlib; do\n       ar x \\$lib\n   done\n   \\`\\`\\`\n3. Run the linking script:\n   \\`\\`\\`\n   ./link_${program}_${target}.sh\n   \\`\\`\\`\n4. Test the resulting executable:\n   \\`\\`\\`\n   ./$output\n   \\`\\`\\`\n\n## Linker used: $(get_linker_for_target \"$target\")\n## Assembler used: $(get_assembler_for_target \"$target\")\n\nGenerated on: $(date)\nEOF\n    \n    print_success \"Created linking package: $package_dir/\"\n    print_info \"Transfer this directory to Plan 9 and follow the README instructions\"\n    \n    echo\n    print_info \"Summary:\"\n    echo \"  Target: $target\"\n    echo \"  Program: $program\"\n    echo \"  Output: $output\"\n    echo \"  Package: $package_dir/\"\n    echo \"  Linker: $(get_linker_for_target \"$target\")\"\n}\n\n# Run main function with all arguments\nmain \"$@\"\n"}