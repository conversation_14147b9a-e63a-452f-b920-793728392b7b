{"path": {"rootPath": "/Users/<USER>/Documents/augment-projects/9plan/plan9front", "relPath": "rust-plan9-integration/tools/build-scripts/cross-compile-plan9.sh"}, "originalCode": "#!/bin/bash\n# Cross-compilation script for Plan 9 targets\n# This script builds the Rust Plan 9 runtime for Plan 9 targets\n\nset -e\n\n# Colors for output\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nNC='\\033[0m' # No Color\n\n# Configuration\nPROJECT_ROOT=\"$(cd \"$(dirname \"${BASH_SOURCE[0]}\")/../..\" && pwd)\"\nTARGET_SPEC_DIR=\"$PROJECT_ROOT/target-specs/plan9-targets\"\nRUNTIME_DIR=\"$PROJECT_ROOT/runtime\"\nTESTS_DIR=\"$PROJECT_ROOT/tests\"\n\n# Available targets\nTARGETS=(\"x86_64-unknown-plan9\" \"i386-unknown-plan9\")\n\nprint_header() {\n    echo -e \"${BLUE}================================${NC}\"\n    echo -e \"${BLUE}  Rust Plan 9 Cross-Compiler${NC}\"\n    echo -e \"${BLUE}================================${NC}\"\n    echo\n}\n\nprint_success() {\n    echo -e \"${GREEN}✓ $1${NC}\"\n}\n\nprint_error() {\n    echo -e \"${RED}✗ $1${NC}\"\n}\n\nprint_warning() {\n    echo -e \"${YELLOW}⚠ $1${NC}\"\n}\n\nprint_info() {\n    echo -e \"${BLUE}ℹ $1${NC}\"\n}\n\ncheck_prerequisites() {\n    print_info \"Checking prerequisites...\"\n    \n    # Check for Rust nightly\n    if ! command -v rustc +nightly &> /dev/null; then\n        print_error \"Rust nightly toolchain not found\"\n        echo \"Please install with: rustup install nightly\"\n        exit 1\n    fi\n    \n    # Check for cargo\n    if ! command -v cargo &> /dev/null; then\n        print_error \"Cargo not found\"\n        exit 1\n    fi\n    \n    print_success \"Prerequisites check passed\"\n}\n\nbuild_runtime() {\n    local target=$1\n    local target_file=\"$TARGET_SPEC_DIR/$target.json\"\n    \n    print_info \"Building runtime for target: $target\"\n    \n    if [ ! -f \"$target_file\" ]; then\n        print_error \"Target specification not found: $target_file\"\n        return 1\n    fi\n    \n    cd \"$RUNTIME_DIR\"\n    \n    # Build the runtime library\n    if cargo +nightly build --target \"$target_file\" -Zbuild-std=core,alloc --release; then\n        print_success \"Runtime built successfully for $target\"\n        return 0\n    else\n        print_error \"Failed to build runtime for $target\"\n        return 1\n    fi\n}\n\nbuild_tests() {\n    local target=$1\n    local target_file=\"$TARGET_SPEC_DIR/$target.json\"\n    \n    print_info \"Building tests for target: $target (library only)\"\n    \n    # Build basic tests (library only, since linking requires Plan 9 tools)\n    cd \"$TESTS_DIR/basic\"\n    if cargo +nightly build --target \"$target_file\" -Zbuild-std=core,alloc --lib 2>/dev/null; then\n        print_success \"Basic tests built (library) for $target\"\n    else\n        print_warning \"Basic tests failed to build for $target (expected without Plan 9 linker)\"\n    fi\n    \n    # Build integration tests (library only)\n    cd \"$TESTS_DIR/integration\"\n    if cargo +nightly build --target \"$target_file\" -Zbuild-std=core,alloc --lib 2>/dev/null; then\n        print_success \"Integration tests built (library) for $target\"\n    else\n        print_warning \"Integration tests failed to build for $target (expected without Plan 9 linker)\"\n    fi\n}\n\nshow_usage() {\n    echo \"Usage: $0 [OPTIONS] [TARGET]\"\n    echo\n    echo \"OPTIONS:\"\n    echo \"  -h, --help     Show this help message\"\n    echo \"  -r, --runtime  Build only runtime library\"\n    echo \"  -t, --tests    Build only tests\"\n    echo \"  -a, --all      Build for all targets (default)\"\n    echo\n    echo \"TARGETS:\"\n    for target in \"${TARGETS[@]}\"; do\n        echo \"  $target\"\n    done\n    echo\n    echo \"If no target is specified, builds for all available targets.\"\n}\n\nmain() {\n    local build_runtime=true\n    local build_tests=true\n    local specific_target=\"\"\n    \n    # Parse command line arguments\n    while [[ $# -gt 0 ]]; do\n        case $1 in\n            -h|--help)\n                show_usage\n                exit 0\n                ;;\n            -r|--runtime)\n                build_tests=false\n                shift\n                ;;\n            -t|--tests)\n                build_runtime=false\n                shift\n                ;;\n            -a|--all)\n                # Default behavior\n                shift\n                ;;\n            x86_64-unknown-plan9|i386-unknown-plan9)\n                specific_target=$1\n                shift\n                ;;\n            *)\n                print_error \"Unknown option: $1\"\n                show_usage\n                exit 1\n                ;;\n        esac\n    done\n    \n    print_header\n    check_prerequisites\n    \n    # Determine which targets to build\n    local targets_to_build=()\n    if [ -n \"$specific_target\" ]; then\n        targets_to_build=(\"$specific_target\")\n    else\n        targets_to_build=(\"${TARGETS[@]}\")\n    fi\n    \n    # Build for each target\n    local success_count=0\n    local total_count=${#targets_to_build[@]}\n    \n    for target in \"${targets_to_build[@]}\"; do\n        echo\n        print_info \"Processing target: $target\"\n        echo \"----------------------------------------\"\n        \n        local target_success=true\n        \n        if [ \"$build_runtime\" = true ]; then\n            if ! build_runtime \"$target\"; then\n                target_success=false\n            fi\n        fi\n        \n        if [ \"$build_tests\" = true ]; then\n            build_tests \"$target\"\n        fi\n        \n        if [ \"$target_success\" = true ]; then\n            ((success_count++))\n        fi\n    done\n    \n    # Summary\n    echo\n    echo \"========================================\"\n    print_info \"Build Summary\"\n    echo \"========================================\"\n    print_info \"Targets processed: $total_count\"\n    print_success \"Successful builds: $success_count\"\n    \n    if [ $success_count -eq $total_count ]; then\n        print_success \"All builds completed successfully!\"\n        echo\n        print_info \"Next steps:\"\n        echo \"  1. Set up Plan 9 QEMU environment\"\n        echo \"  2. Install Plan 9 development tools (6l, 8l linkers)\"\n        echo \"  3. Test binaries in actual Plan 9 system\"\n    else\n        print_warning \"Some builds failed. Check the output above for details.\"\n    fi\n}\n\n# Run main function with all arguments\nmain \"$@\"\n", "modifiedCode": "#!/bin/bash\n# Cross-compilation script for Plan 9 targets\n# This script builds the Rust Plan 9 runtime for Plan 9 targets\n\nset -e\n\n# Colors for output\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nNC='\\033[0m' # No Color\n\n# Configuration\nPROJECT_ROOT=\"$(cd \"$(dirname \"${BASH_SOURCE[0]}\")/../..\" && pwd)\"\nTARGET_SPEC_DIR=\"$PROJECT_ROOT/target-specs/plan9-targets\"\nRUNTIME_DIR=\"$PROJECT_ROOT/runtime\"\nTESTS_DIR=\"$PROJECT_ROOT/tests\"\n\n# Available targets\nTARGETS=(\"x86_64-unknown-plan9\" \"i386-unknown-plan9\")\n\nprint_header() {\n    echo -e \"${BLUE}================================${NC}\"\n    echo -e \"${BLUE}  Rust Plan 9 Cross-Compiler${NC}\"\n    echo -e \"${BLUE}================================${NC}\"\n    echo\n}\n\nprint_success() {\n    echo -e \"${GREEN}✓ $1${NC}\"\n}\n\nprint_error() {\n    echo -e \"${RED}✗ $1${NC}\"\n}\n\nprint_warning() {\n    echo -e \"${YELLOW}⚠ $1${NC}\"\n}\n\nprint_info() {\n    echo -e \"${BLUE}ℹ $1${NC}\"\n}\n\ncheck_prerequisites() {\n    print_info \"Checking prerequisites...\"\n    \n    # Check for Rust nightly\n    if ! command -v rustc +nightly &> /dev/null; then\n        print_error \"Rust nightly toolchain not found\"\n        echo \"Please install with: rustup install nightly\"\n        exit 1\n    fi\n    \n    # Check for cargo\n    if ! command -v cargo &> /dev/null; then\n        print_error \"Cargo not found\"\n        exit 1\n    fi\n    \n    print_success \"Prerequisites check passed\"\n}\n\nbuild_runtime() {\n    local target=$1\n    local target_file=\"$TARGET_SPEC_DIR/$target.json\"\n    \n    print_info \"Building runtime for target: $target\"\n    \n    if [ ! -f \"$target_file\" ]; then\n        print_error \"Target specification not found: $target_file\"\n        return 1\n    fi\n    \n    cd \"$RUNTIME_DIR\"\n    \n    # Build the runtime library\n    if cargo +nightly build --target \"$target_file\" -Zbuild-std=core,alloc --release; then\n        print_success \"Runtime built successfully for $target\"\n        return 0\n    else\n        print_error \"Failed to build runtime for $target\"\n        return 1\n    fi\n}\n\nbuild_tests() {\n    local target=$1\n    local target_file=\"$TARGET_SPEC_DIR/$target.json\"\n    \n    print_info \"Building tests for target: $target (library only)\"\n    \n    # Build basic tests (library only, since linking requires Plan 9 tools)\n    cd \"$TESTS_DIR/basic\"\n    if cargo +nightly build --target \"$target_file\" -Zbuild-std=core,alloc --lib 2>/dev/null; then\n        print_success \"Basic tests built (library) for $target\"\n    else\n        print_warning \"Basic tests failed to build for $target (expected without Plan 9 linker)\"\n    fi\n    \n    # Build integration tests (library only)\n    cd \"$TESTS_DIR/integration\"\n    if cargo +nightly build --target \"$target_file\" -Zbuild-std=core,alloc --lib 2>/dev/null; then\n        print_success \"Integration tests built (library) for $target\"\n    else\n        print_warning \"Integration tests failed to build for $target (expected without Plan 9 linker)\"\n    fi\n}\n\nshow_usage() {\n    echo \"Usage: $0 [OPTIONS] [TARGET]\"\n    echo\n    echo \"OPTIONS:\"\n    echo \"  -h, --help     Show this help message\"\n    echo \"  -r, --runtime  Build only runtime library\"\n    echo \"  -t, --tests    Build only tests\"\n    echo \"  -a, --all      Build for all targets (default)\"\n    echo\n    echo \"TARGETS:\"\n    for target in \"${TARGETS[@]}\"; do\n        echo \"  $target\"\n    done\n    echo\n    echo \"If no target is specified, builds for all available targets.\"\n}\n\nmain() {\n    local build_runtime=true\n    local build_tests=true\n    local specific_target=\"\"\n    \n    # Parse command line arguments\n    while [[ $# -gt 0 ]]; do\n        case $1 in\n            -h|--help)\n                show_usage\n                exit 0\n                ;;\n            -r|--runtime)\n                build_tests=false\n                shift\n                ;;\n            -t|--tests)\n                build_runtime=false\n                shift\n                ;;\n            -a|--all)\n                # Default behavior\n                shift\n                ;;\n            x86_64-unknown-plan9|i386-unknown-plan9)\n                specific_target=$1\n                shift\n                ;;\n            *)\n                print_error \"Unknown option: $1\"\n                show_usage\n                exit 1\n                ;;\n        esac\n    done\n    \n    print_header\n    check_prerequisites\n    \n    # Determine which targets to build\n    local targets_to_build=()\n    if [ -n \"$specific_target\" ]; then\n        targets_to_build=(\"$specific_target\")\n    else\n        targets_to_build=(\"${TARGETS[@]}\")\n    fi\n    \n    # Build for each target\n    local success_count=0\n    local total_count=${#targets_to_build[@]}\n    \n    for target in \"${targets_to_build[@]}\"; do\n        echo\n        print_info \"Processing target: $target\"\n        echo \"----------------------------------------\"\n        \n        local target_success=true\n        \n        if [ \"$build_runtime\" = true ]; then\n            if ! build_runtime \"$target\"; then\n                target_success=false\n            fi\n        fi\n        \n        if [ \"$build_tests\" = true ]; then\n            build_tests \"$target\"\n        fi\n        \n        if [ \"$target_success\" = true ]; then\n            ((success_count++))\n        fi\n    done\n    \n    # Summary\n    echo\n    echo \"========================================\"\n    print_info \"Build Summary\"\n    echo \"========================================\"\n    print_info \"Targets processed: $total_count\"\n    print_success \"Successful builds: $success_count\"\n    \n    if [ $success_count -eq $total_count ]; then\n        print_success \"All builds completed successfully!\"\n        echo\n        print_info \"Next steps:\"\n        echo \"  1. Set up Plan 9 QEMU environment: cd tools/qemu-setup/plan9-env && ./start-plan9.sh\"\n        echo \"  2. Install Plan 9 development tools (6l, 8l linkers)\"\n        echo \"  3. Test binaries in actual Plan 9 system\"\n        echo \"  4. Use tools/build-scripts/link-plan9.sh for linking in Plan 9\"\n    else\n        print_warning \"Some builds failed. Check the output above for details.\"\n    fi\n}\n\n# Run main function with all arguments\nmain \"$@\"\n"}