use axum::{
    extract::State,
    response::Json,
    routing::post,
    Router,
};
use serde_json::{json, Value};
use utoipa;
use validator::Validate;

use crate::{
    dto::auth::*,
    handlers::{json_response, ApiError},
    services::UserService,
    AppState
};

/// Authentication routes
pub fn auth_routes() -> Router<AppState> {
    Router::new()
        .route("/login", post(login))
        .route("/logout", post(logout))
        .route("/refresh", post(refresh_token))
}

/// Login endpoint
pub async fn login(
    State(state): State<AppState>,
    Json(request): Json<LoginRequest>,
) -> Result<Json<Value>, ApiError> {
    // Validate request
    request.validate().map_err(|_| ApiError::BadRequest("Invalid login request".to_string()))?;

    // Get user service
    let user_service = UserService::new(state.db.clone(), state.auth_service.clone());

    // Find user by email
    let user = user_service.get_user_by_email(&request.email).await?;

    // Verify password
    let is_valid = state
        .auth_service
        .verify_password(&request.password, &user.password_hash)
        .map_err(|e| ApiError::InternalServerError(format!("Failed to verify password: {}", e)))?;

    if !is_valid {
        return Err(ApiError::BadRequest("Invalid email or password".to_string()));
    }

    // Check if user is active
    if !user.is_active {
        return Err(ApiError::BadRequest("Account is disabled".to_string()));
    }

    // Generate JWT token
    let token = state
        .auth_service
        .generate_token(
            user.id,
            user.email.clone(),
            user.role.to_string(),
            user.organization_id,
        )
        .map_err(|e| ApiError::InternalServerError(format!("Failed to generate token: {}", e)))?;

    // Update last login
    if let Err(e) = user_service.update_last_login(user.id).await {
        // Log error but don't fail the login
        tracing::warn!("Failed to update last login for user {}: {}", user.id, e);
    }

    // Create response
    let response = LoginResponse {
        token,
        expires_at: (chrono::Utc::now() + chrono::Duration::hours(8)).to_rfc3339(),
        user: UserInfo {
            id: user.id.to_string(),
            email: user.email,
            full_name: user.full_name,
            role: user.role.to_string(),
            organization_id: user.organization_id.map(|id| id.to_string()),
        },
    };

    Ok(json_response("Login successful", response))
}

/// Logout endpoint
pub async fn logout(
    State(_state): State<AppState>,
) -> Result<Json<Value>, ApiError> {
    // TODO: Implement session invalidation
    Ok(Json(json!({
        "success": true,
        "message": "Logout successful"
    })))
}

/// Refresh token endpoint
pub async fn refresh_token(
    State(state): State<AppState>,
    Json(request): Json<RefreshTokenRequest>,
) -> Result<Json<Value>, ApiError> {
    // Validate request
    request.validate().map_err(|_| ApiError::BadRequest("Invalid refresh token request".to_string()))?;

    // Validate and refresh the token using auth service
    let new_token = state
        .auth_service
        .refresh_token(&request.token)
        .map_err(|e| match e {
            auth::AuthError::InvalidToken => ApiError::Unauthorized("Invalid or expired token".to_string()),
            _ => ApiError::InternalServerError(format!("Failed to refresh token: {}", e)),
        })?;

    // Calculate expiration time (8 hours from now)
    let expires_at = (chrono::Utc::now() + chrono::Duration::hours(8)).to_rfc3339();

    let response = RefreshTokenResponse {
        token: new_token,
        expires_at,
    };

    Ok(json_response("Token refreshed successfully", response))
}

/// Register endpoint
pub async fn register(
    State(_state): State<AppState>,
    Json(_register_request): Json<RegisterRequest>,
) -> Result<Json<Value>, ApiError> {
    // TODO: Implement user registration logic
    Err(ApiError::InternalServerError("Registration not implemented yet".to_string()))
}


