use axum::{
    extract::{Path, Query, Request, State},
    response::Json,
    routing::{delete, get, post, put},
    Router,
};
use database::entities::{ProjectType, ProjectStatus};
use serde::Deserialize;
use serde_json::Value;
use uuid::Uuid;
use validator::Validate;

use crate::{
    handlers::{json_response, ApiError},
    services::{CreateProjectRequest, ProjectService, UpdateProjectRequest},
    AppState,
};

/// Query parameters for project listing
#[derive(Debug, Deserialize, Validate)]
pub struct ProjectListQuery {
    #[validate(range(min = 1))]
    pub page: Option<u64>,

    #[validate(range(min = 1, max = 100))]
    pub per_page: Option<u64>,

    pub organization_id: Option<Uuid>,
    pub status: Option<ProjectStatus>,
    pub project_type: Option<ProjectType>,
}

/// Project management routes
pub fn project_routes() -> Router<AppState> {
    Router::new()
        .route("/", get(list_projects).post(create_project))
        .route("/:id", get(get_project).put(update_project).delete(delete_project))
        .route("/:id/data", get(get_project_data).post(create_project_data).put(update_project_data))
        .route("/:id/data/:data_id", get(get_project_data_version).delete(delete_project_data_version))
        .route("/:id/clone", post(clone_project))
        .route("/:id/financial-analysis", get(get_project_financial_analysis))
        .route("/:id/export", get(export_project_data))
}

/// Create project endpoint
pub async fn create_project(
    State(state): State<AppState>,
    request: Request,
) -> Result<Json<Value>, ApiError> {
    // Extract user claims from request extensions
    let claims = request
        .extensions()
        .get::<auth::Claims>()
        .cloned()
        .ok_or_else(|| ApiError::Unauthorized("Authentication required".to_string()))?;

    // Extract JSON body manually
    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await
        .map_err(|e| ApiError::BadRequest(format!("Failed to read request body: {}", e)))?;

    let create_request: CreateProjectRequest = serde_json::from_slice(&bytes)
        .map_err(|e| ApiError::BadRequest(format!("Invalid JSON: {}", e)))?;

    let project_service = ProjectService::new(state.db.clone());
    let project = project_service.create_project(create_request, &claims).await?;

    Ok(json_response("Project created successfully", project))
}

/// Get project endpoint
pub async fn get_project(
    State(state): State<AppState>,
    Path(project_id): Path<Uuid>,
    request: Request,
) -> Result<Json<Value>, ApiError> {
    // Extract user claims from request extensions
    let claims = request
        .extensions()
        .get::<auth::Claims>()
        .cloned()
        .ok_or_else(|| ApiError::Unauthorized("Authentication required".to_string()))?;

    let project_service = ProjectService::new(state.db.clone());
    let project = project_service.get_project(project_id, &claims).await?;

    Ok(json_response("Project retrieved successfully", project))
}

/// Update project endpoint
pub async fn update_project(
    State(state): State<AppState>,
    Path(project_id): Path<Uuid>,
    request: Request,
) -> Result<Json<Value>, ApiError> {
    // Extract user claims from request extensions
    let claims = request
        .extensions()
        .get::<auth::Claims>()
        .cloned()
        .ok_or_else(|| ApiError::Unauthorized("Authentication required".to_string()))?;

    // Extract JSON body manually
    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await
        .map_err(|e| ApiError::BadRequest(format!("Failed to read request body: {}", e)))?;

    let update_request: UpdateProjectRequest = serde_json::from_slice(&bytes)
        .map_err(|e| ApiError::BadRequest(format!("Invalid JSON: {}", e)))?;

    let project_service = ProjectService::new(state.db.clone());
    let project = project_service.update_project(project_id, update_request, &claims).await?;

    Ok(json_response("Project updated successfully", project))
}

/// List projects endpoint
pub async fn list_projects(
    State(state): State<AppState>,
    Query(query): Query<ProjectListQuery>,
    request: Request,
) -> Result<Json<Value>, ApiError> {
    // Validate query parameters
    query.validate().map_err(|e| ApiError::BadRequest(format!("Invalid query parameters: {}", e)))?;

    // Extract user claims from request extensions
    let claims = request
        .extensions()
        .get::<auth::Claims>()
        .cloned()
        .ok_or_else(|| ApiError::Unauthorized("Authentication required".to_string()))?;

    let page = query.page.unwrap_or(1);
    let per_page = query.per_page.unwrap_or(20);

    let project_service = ProjectService::new(state.db.clone());
    let projects = project_service
        .list_projects(
            page,
            per_page,
            query.organization_id,
            query.status,
            query.project_type,
            &claims,
        )
        .await?;

    Ok(json_response("Projects retrieved successfully", projects))
}

// TODO: Implement project data management endpoints when project data service is ready
/*
/// Get project data endpoint
pub async fn get_project_data(
    State(state): State<AppState>,
    Path(project_id): Path<Uuid>,
    request: Request,
) -> Result<Json<Value>, ApiError> {
    // Extract user claims from request extensions
    let claims = request
        .extensions()
        .get::<auth::Claims>()
        .cloned()
        .ok_or_else(|| ApiError::Unauthorized("Authentication required".to_string()))?;

    let project_service = ProjectService::new(state.db.clone());
    let project_data = project_service.get_project_data(project_id, &claims).await?;

    Ok(json_response("Project data retrieved successfully", project_data))
}

/// Create project data endpoint
pub async fn create_project_data(
    State(state): State<AppState>,
    Path(project_id): Path<Uuid>,
    request: Request,
) -> Result<Json<Value>, ApiError> {
    // Extract user claims from request extensions
    let claims = request
        .extensions()
        .get::<auth::Claims>()
        .cloned()
        .ok_or_else(|| ApiError::Unauthorized("Authentication required".to_string()))?;

    // Extract JSON body manually
    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await
        .map_err(|e| ApiError::BadRequest(format!("Failed to read request body: {}", e)))?;

    let data_request: serde_json::Value = serde_json::from_slice(&bytes)
        .map_err(|e| ApiError::BadRequest(format!("Invalid JSON: {}", e)))?;

    let project_service = ProjectService::new(state.db.clone());
    let project_data = project_service.create_project_data(project_id, data_request, &claims).await?;

    Ok(json_response("Project data created successfully", project_data))
}

/// Update project data endpoint
pub async fn update_project_data(
    State(state): State<AppState>,
    Path(project_id): Path<Uuid>,
    request: Request,
) -> Result<Json<Value>, ApiError> {
    // Extract user claims from request extensions
    let claims = request
        .extensions()
        .get::<auth::Claims>()
        .cloned()
        .ok_or_else(|| ApiError::Unauthorized("Authentication required".to_string()))?;

    // Extract JSON body manually
    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await
        .map_err(|e| ApiError::BadRequest(format!("Failed to read request body: {}", e)))?;

    let data_request: serde_json::Value = serde_json::from_slice(&bytes)
        .map_err(|e| ApiError::BadRequest(format!("Invalid JSON: {}", e)))?;

    let project_service = ProjectService::new(state.db.clone());
    let project_data = project_service.update_project_data(project_id, data_request, &claims).await?;

    Ok(json_response("Project data updated successfully", project_data))
}

/// Get project data version endpoint
pub async fn get_project_data_version(
    State(state): State<AppState>,
    Path((project_id, data_id)): Path<(Uuid, Uuid)>,
    request: Request,
) -> Result<Json<Value>, ApiError> {
    // Extract user claims from request extensions
    let claims = request
        .extensions()
        .get::<auth::Claims>()
        .cloned()
        .ok_or_else(|| ApiError::Unauthorized("Authentication required".to_string()))?;

    let project_service = ProjectService::new(state.db.clone());
    let project_data = project_service.get_project_data_version(project_id, data_id, &claims).await?;

    Ok(json_response("Project data version retrieved successfully", project_data))
}

/// Delete project data version endpoint
pub async fn delete_project_data_version(
    State(state): State<AppState>,
    Path((project_id, data_id)): Path<(Uuid, Uuid)>,
    request: Request,
) -> Result<Json<Value>, ApiError> {
    // Extract user claims from request extensions
    let claims = request
        .extensions()
        .get::<auth::Claims>()
        .cloned()
        .ok_or_else(|| ApiError::Unauthorized("Authentication required".to_string()))?;

    let project_service = ProjectService::new(state.db.clone());
    project_service.delete_project_data_version(project_id, data_id, &claims).await?;

    Ok(json_response("Project data version deleted successfully", ()))
}

/// Clone project endpoint
pub async fn clone_project(
    State(state): State<AppState>,
    Path(project_id): Path<Uuid>,
    request: Request,
) -> Result<Json<Value>, ApiError> {
    // Extract user claims from request extensions
    let claims = request
        .extensions()
        .get::<auth::Claims>()
        .cloned()
        .ok_or_else(|| ApiError::Unauthorized("Authentication required".to_string()))?;

    // Extract JSON body manually for clone options
    let bytes = axum::body::to_bytes(request.into_body(), usize::MAX).await
        .map_err(|e| ApiError::BadRequest(format!("Failed to read request body: {}", e)))?;

    let clone_request: serde_json::Value = serde_json::from_slice(&bytes)
        .map_err(|e| ApiError::BadRequest(format!("Invalid JSON: {}", e)))?;

    let new_name = clone_request.get("name")
        .and_then(|v| v.as_str())
        .unwrap_or("Copy of Project");

    let project_service = ProjectService::new(state.db.clone());
    let cloned_project = project_service.clone_project(project_id, new_name.to_string(), &claims).await?;

    Ok(json_response("Project cloned successfully", cloned_project))
}
*/

/// Delete project endpoint
pub async fn delete_project(
    State(state): State<AppState>,
    Path(project_id): Path<Uuid>,
    request: Request,
) -> Result<Json<Value>, ApiError> {
    // Extract user claims from request extensions
    let claims = request
        .extensions()
        .get::<auth::Claims>()
        .cloned()
        .ok_or_else(|| ApiError::Unauthorized("Authentication required".to_string()))?;

    let project_service = ProjectService::new(state.db.clone());
    project_service.delete_project(project_id, &claims).await?;

    Ok(json_response("Project deleted successfully", ()))
}

/// Get project data endpoint
pub async fn get_project_data(
    State(state): State<AppState>,
    Path(project_id): Path<Uuid>,
    request: Request,
) -> Result<Json<Value>, ApiError> {
    // Extract user claims from request extensions
    let claims = request
        .extensions()
        .get::<auth::Claims>()
        .cloned()
        .ok_or_else(|| ApiError::Unauthorized("Authentication required".to_string()))?;

    // Get project service
    let project_service = ProjectService::new(state.db.clone());

    // Verify user has access to project
    let _project = project_service.get_project(project_id, &claims).await?;

    // Get project data (this would typically fetch from project_data table)
    // For now, return a placeholder response
    let project_data = serde_json::json!({
        "project_id": project_id,
        "data_type": "financial_model",
        "version": "1.0.0",
        "created_at": chrono::Utc::now().to_rfc3339(),
        "data": {
            "land_cost": 1000000000,
            "construction_cost": 2000000000,
            "total_investment": 3000000000,
            "expected_revenue": 4000000000,
            "roi_percentage": 33.33
        }
    });

    Ok(json_response("Project data retrieved successfully", project_data))
}

/// Create project data endpoint
pub async fn create_project_data(
    State(state): State<AppState>,
    Path(project_id): Path<Uuid>,
    Json(data_request): Json<serde_json::Value>,
    request: Request,
) -> Result<Json<Value>, ApiError> {
    // Extract user claims from request extensions
    let claims = request
        .extensions()
        .get::<auth::Claims>()
        .cloned()
        .ok_or_else(|| ApiError::Unauthorized("Authentication required".to_string()))?;

    // Get project service
    let project_service = ProjectService::new(state.db.clone());

    // Verify user has access to project
    let _project = project_service.get_project(project_id, &claims).await?;

    // Create project data entry
    let data_id = Uuid::new_v4();
    let created_data = serde_json::json!({
        "id": data_id,
        "project_id": project_id,
        "data": data_request,
        "version": "1.0.0",
        "created_at": chrono::Utc::now().to_rfc3339(),
        "created_by": claims.sub
    });

    Ok(json_response("Project data created successfully", created_data))
}

/// Update project data endpoint
pub async fn update_project_data(
    State(state): State<AppState>,
    Path(project_id): Path<Uuid>,
    Json(data_request): Json<serde_json::Value>,
    request: Request,
) -> Result<Json<Value>, ApiError> {
    // Extract user claims from request extensions
    let claims = request
        .extensions()
        .get::<auth::Claims>()
        .cloned()
        .ok_or_else(|| ApiError::Unauthorized("Authentication required".to_string()))?;

    // Get project service
    let project_service = ProjectService::new(state.db.clone());

    // Verify user has access to project
    let _project = project_service.get_project(project_id, &claims).await?;

    // Update project data
    let updated_data = serde_json::json!({
        "project_id": project_id,
        "data": data_request,
        "version": "1.1.0",
        "updated_at": chrono::Utc::now().to_rfc3339(),
        "updated_by": claims.sub
    });

    Ok(json_response("Project data updated successfully", updated_data))
}

/// Get project data version endpoint
pub async fn get_project_data_version(
    State(state): State<AppState>,
    Path((project_id, data_id)): Path<(Uuid, Uuid)>,
    request: Request,
) -> Result<Json<Value>, ApiError> {
    // Extract user claims from request extensions
    let claims = request
        .extensions()
        .get::<auth::Claims>()
        .cloned()
        .ok_or_else(|| ApiError::Unauthorized("Authentication required".to_string()))?;

    // Get project service
    let project_service = ProjectService::new(state.db.clone());

    // Verify user has access to project
    let _project = project_service.get_project(project_id, &claims).await?;

    // Get specific data version
    let data_version = serde_json::json!({
        "id": data_id,
        "project_id": project_id,
        "version": "1.0.0",
        "created_at": chrono::Utc::now().to_rfc3339(),
        "data": {
            "financial_model": "Vietnamese real estate calculations",
            "status": "active"
        }
    });

    Ok(json_response("Project data version retrieved successfully", data_version))
}

/// Delete project data version endpoint
pub async fn delete_project_data_version(
    State(state): State<AppState>,
    Path((project_id, data_id)): Path<(Uuid, Uuid)>,
    request: Request,
) -> Result<Json<Value>, ApiError> {
    // Extract user claims from request extensions
    let claims = request
        .extensions()
        .get::<auth::Claims>()
        .cloned()
        .ok_or_else(|| ApiError::Unauthorized("Authentication required".to_string()))?;

    // Get project service
    let project_service = ProjectService::new(state.db.clone());

    // Verify user has access to project
    let _project = project_service.get_project(project_id, &claims).await?;

    // Check permissions - only project owners and admins can delete data
    let user_id = Uuid::parse_str(&claims.sub)
        .map_err(|_| ApiError::BadRequest("Invalid user ID".to_string()))?;

    if claims.role != "admin" && _project.owner_id.to_string() != claims.sub {
        return Err(ApiError::Forbidden("Only project owners and admins can delete project data".to_string()));
    }

    // Delete project data version (soft delete)
    tracing::info!("Deleting project data version {} for project {}", data_id, project_id);

    Ok(json_response("Project data version deleted successfully", ()))
}

/// Clone project endpoint
pub async fn clone_project(
    State(state): State<AppState>,
    Path(project_id): Path<Uuid>,
    Json(clone_request): Json<serde_json::Value>,
    request: Request,
) -> Result<Json<Value>, ApiError> {
    // Extract user claims from request extensions
    let claims = request
        .extensions()
        .get::<auth::Claims>()
        .cloned()
        .ok_or_else(|| ApiError::Unauthorized("Authentication required".to_string()))?;

    // Get project service
    let project_service = ProjectService::new(state.db.clone());

    // Get original project
    let original_project = project_service.get_project(project_id, &claims).await?;

    // Create clone with new name
    let clone_name = clone_request
        .get("name")
        .and_then(|v| v.as_str())
        .unwrap_or(&format!("{} (Copy)", original_project.name));

    let cloned_project = serde_json::json!({
        "id": Uuid::new_v4(),
        "name": clone_name,
        "description": format!("Cloned from: {}", original_project.description.unwrap_or_default()),
        "project_type": original_project.project_type,
        "owner_id": claims.sub,
        "organization_id": original_project.organization_id,
        "created_at": chrono::Utc::now().to_rfc3339(),
        "cloned_from": project_id
    });

    Ok(json_response("Project cloned successfully", cloned_project))
}
