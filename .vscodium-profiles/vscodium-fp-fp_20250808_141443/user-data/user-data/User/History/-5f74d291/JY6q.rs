pub mod response_optimizer;
pub mod caching;
pub mod memory_manager;
pub mod scaling;

pub use response_optimizer::*;
pub use caching::*;
pub use memory_manager::*;
pub use scaling::*;

use std::time::{Duration, Instant};
use tracing::{info, warn};

/// Performance configuration for the application
#[derive(Debug, <PERSON>lone)]
pub struct PerformanceConfig {
    /// Target response time for 95th percentile (milliseconds)
    pub target_p95_response_time_ms: u64,
    /// Target response time for 99th percentile (milliseconds)
    pub target_p99_response_time_ms: u64,
    /// Maximum allowed memory usage per instance (MB)
    pub max_memory_usage_mb: u64,
    /// Cache hit ratio target (0.0 to 1.0)
    pub cache_hit_ratio_target: f64,
    /// Enable performance monitoring
    pub enable_monitoring: bool,
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            target_p95_response_time_ms: 200,
            target_p99_response_time_ms: 500,
            max_memory_usage_mb: 512,
            cache_hit_ratio_target: 0.85,
            enable_monitoring: true,
        }
    }
}

/// Performance metrics collector
#[derive(Debug, <PERSON>lone)]
pub struct PerformanceMetrics {
    pub response_times: Vec<Duration>,
    pub memory_usage_mb: f64,
    pub cache_hit_ratio: f64,
    pub active_connections: u32,
    pub requests_per_second: f64,
}

impl PerformanceMetrics {
    pub fn new() -> Self {
        Self {
            response_times: Vec::new(),
            memory_usage_mb: 0.0,
            cache_hit_ratio: 0.0,
            active_connections: 0,
            requests_per_second: 0.0,
        }
    }

    /// Calculate 95th percentile response time
    pub fn p95_response_time(&self) -> Duration {
        if self.response_times.is_empty() {
            return Duration::from_millis(0);
        }

        let mut sorted_times = self.response_times.clone();
        sorted_times.sort();
        let index = (sorted_times.len() as f64 * 0.95) as usize;
        sorted_times.get(index).copied().unwrap_or(Duration::from_millis(0))
    }

    /// Calculate 99th percentile response time
    pub fn p99_response_time(&self) -> Duration {
        if self.response_times.is_empty() {
            return Duration::from_millis(0);
        }

        let mut sorted_times = self.response_times.clone();
        sorted_times.sort();
        let index = (sorted_times.len() as f64 * 0.99) as usize;
        sorted_times.get(index).copied().unwrap_or(Duration::from_millis(0))
    }

    /// Calculate average response time
    pub fn average_response_time(&self) -> Duration {
        if self.response_times.is_empty() {
            return Duration::from_millis(0);
        }

        let total: Duration = self.response_times.iter().sum();
        total / self.response_times.len() as u32
    }

    /// Add a response time measurement
    pub fn record_response_time(&mut self, duration: Duration) {
        self.response_times.push(duration);
        
        // Keep only the last 1000 measurements to prevent memory growth
        if self.response_times.len() > 1000 {
            self.response_times.remove(0);
        }
    }

    /// Check if performance targets are met
    pub fn meets_targets(&self, config: &PerformanceConfig) -> bool {
        let p95_ms = self.p95_response_time().as_millis() as u64;
        let p99_ms = self.p99_response_time().as_millis() as u64;
        
        p95_ms <= config.target_p95_response_time_ms
            && p99_ms <= config.target_p99_response_time_ms
            && self.memory_usage_mb <= config.max_memory_usage_mb as f64
            && self.cache_hit_ratio >= config.cache_hit_ratio_target
    }
}

impl Default for PerformanceMetrics {
    fn default() -> Self {
        Self::new()
    }
}

/// Performance monitor that tracks and reports performance metrics
pub struct PerformanceMonitor {
    config: PerformanceConfig,
    metrics: PerformanceMetrics,
    last_report: Instant,
}

impl PerformanceMonitor {
    pub fn new(config: PerformanceConfig) -> Self {
        Self {
            config,
            metrics: PerformanceMetrics::new(),
            last_report: Instant::now(),
        }
    }

    /// Record a request completion
    pub fn record_request(&mut self, duration: Duration) {
        self.metrics.record_response_time(duration);
        
        // Check if we should report performance
        if self.last_report.elapsed() >= Duration::from_secs(60) {
            self.report_performance();
            self.last_report = Instant::now();
        }
    }

    /// Update memory usage
    pub fn update_memory_usage(&mut self, usage_mb: f64) {
        self.metrics.memory_usage_mb = usage_mb;
    }

    /// Update cache hit ratio
    pub fn update_cache_hit_ratio(&mut self, ratio: f64) {
        self.metrics.cache_hit_ratio = ratio;
    }

    /// Update active connections
    pub fn update_active_connections(&mut self, count: u32) {
        self.metrics.active_connections = count;
    }

    /// Update requests per second
    pub fn update_requests_per_second(&mut self, rps: f64) {
        self.metrics.requests_per_second = rps;
    }

    /// Report current performance metrics
    pub fn report_performance(&self) {
        if !self.config.enable_monitoring {
            return;
        }

        let p95_ms = self.metrics.p95_response_time().as_millis();
        let p99_ms = self.metrics.p99_response_time().as_millis();
        let avg_ms = self.metrics.average_response_time().as_millis();

        info!(
            "Performance Report - P95: {}ms, P99: {}ms, Avg: {}ms, Memory: {:.1}MB, Cache Hit: {:.2}%, RPS: {:.1}",
            p95_ms,
            p99_ms,
            avg_ms,
            self.metrics.memory_usage_mb,
            self.metrics.cache_hit_ratio * 100.0,
            self.metrics.requests_per_second
        );

        // Check if targets are met
        if !self.metrics.meets_targets(&self.config) {
            warn!("Performance targets not met!");
            
            if p95_ms > self.config.target_p95_response_time_ms as u128 {
                warn!("P95 response time ({} ms) exceeds target ({} ms)", 
                      p95_ms, self.config.target_p95_response_time_ms);
            }
            
            if p99_ms > self.config.target_p99_response_time_ms as u128 {
                warn!("P99 response time ({} ms) exceeds target ({} ms)", 
                      p99_ms, self.config.target_p99_response_time_ms);
            }
            
            if self.metrics.memory_usage_mb > self.config.max_memory_usage_mb as f64 {
                warn!("Memory usage ({:.1} MB) exceeds target ({} MB)", 
                      self.metrics.memory_usage_mb, self.config.max_memory_usage_mb);
            }
            
            if self.metrics.cache_hit_ratio < self.config.cache_hit_ratio_target {
                warn!("Cache hit ratio ({:.2}%) below target ({:.2}%)", 
                      self.metrics.cache_hit_ratio * 100.0, 
                      self.config.cache_hit_ratio_target * 100.0);
            }
        }
    }

    /// Get current metrics
    pub fn get_metrics(&self) -> &PerformanceMetrics {
        &self.metrics
    }

    /// Get performance configuration
    pub fn get_config(&self) -> &PerformanceConfig {
        &self.config
    }
}

/// Middleware for measuring request performance
pub async fn performance_middleware<B>(
    req: axum::extract::Request<B>,
    next: axum::middleware::Next<B>,
) -> axum::response::Response {
    let start = Instant::now();
    let method = req.method().clone();
    let uri = req.uri().clone();
    
    let response = next.run(req).await;
    
    let duration = start.elapsed();
    let status = response.status();
    
    // Log slow requests
    if duration > Duration::from_millis(200) {
        warn!(
            "Slow request: {} {} - {}ms (status: {})",
            method,
            uri,
            duration.as_millis(),
            status
        );
    }
    
    // Record metrics in performance monitor
    PERFORMANCE_METRICS.record_request(
        method.as_str(),
        uri.path(),
        status.as_u16(),
        duration,
    );
    
    response
}

/// Performance optimization recommendations
pub struct PerformanceRecommendations;

impl PerformanceRecommendations {
    /// Generate performance optimization recommendations
    pub fn generate(metrics: &PerformanceMetrics, config: &PerformanceConfig) -> Vec<String> {
        let mut recommendations = Vec::new();
        
        let p95_ms = metrics.p95_response_time().as_millis() as u64;
        let p99_ms = metrics.p99_response_time().as_millis() as u64;
        
        // Response time recommendations
        if p95_ms > config.target_p95_response_time_ms {
            recommendations.push(format!(
                "P95 response time ({} ms) exceeds target. Consider: database query optimization, caching, or connection pooling",
                p95_ms
            ));
        }
        
        if p99_ms > config.target_p99_response_time_ms {
            recommendations.push(format!(
                "P99 response time ({} ms) exceeds target. Consider: async processing, request queuing, or load balancing",
                p99_ms
            ));
        }
        
        // Memory recommendations
        if metrics.memory_usage_mb > config.max_memory_usage_mb as f64 {
            recommendations.push(format!(
                "Memory usage ({:.1} MB) exceeds target. Consider: memory profiling, garbage collection tuning, or data structure optimization",
                metrics.memory_usage_mb
            ));
        }
        
        // Cache recommendations
        if metrics.cache_hit_ratio < config.cache_hit_ratio_target {
            recommendations.push(format!(
                "Cache hit ratio ({:.2}%) below target. Consider: cache warming, TTL optimization, or cache key strategy review",
                metrics.cache_hit_ratio * 100.0
            ));
        }
        
        // Connection recommendations
        if metrics.active_connections > 100 {
            recommendations.push(
                "High number of active connections. Consider: connection pooling optimization or load balancing".to_string()
            );
        }
        
        // RPS recommendations
        if metrics.requests_per_second > 1000.0 {
            recommendations.push(
                "High request rate. Consider: horizontal scaling, caching, or rate limiting".to_string()
            );
        }
        
        if recommendations.is_empty() {
            recommendations.push("Performance targets are being met. Continue monitoring.".to_string());
        }
        
        recommendations
    }
}
