#!/bin/bash
# Cross-compilation script for Plan 9 targets
# This script builds the Rust Plan 9 runtime for Plan 9 targets

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
TARGET_SPEC_DIR="$PROJECT_ROOT/target-specs/plan9-targets"
RUNTIME_DIR="$PROJECT_ROOT/runtime"
TESTS_DIR="$PROJECT_ROOT/tests"

# Available targets
TARGETS=("x86_64-unknown-plan9" "i386-unknown-plan9")

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Rust Plan 9 Cross-Compiler${NC}"
    echo -e "${BLUE}================================${NC}"
    echo
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check for Rust nightly
    if ! command -v rustc +nightly &> /dev/null; then
        print_error "Rust nightly toolchain not found"
        echo "Please install with: rustup install nightly"
        exit 1
    fi
    
    # Check for cargo
    if ! command -v cargo &> /dev/null; then
        print_error "Cargo not found"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

build_runtime() {
    local target=$1
    local target_file="$TARGET_SPEC_DIR/$target.json"
    
    print_info "Building runtime for target: $target"
    
    if [ ! -f "$target_file" ]; then
        print_error "Target specification not found: $target_file"
        return 1
    fi
    
    cd "$RUNTIME_DIR"
    
    # Build the runtime library
    if cargo +nightly build --target "$target_file" -Zbuild-std=core,alloc --release; then
        print_success "Runtime built successfully for $target"
        return 0
    else
        print_error "Failed to build runtime for $target"
        return 1
    fi
}

build_tests() {
    local target=$1
    local target_file="$TARGET_SPEC_DIR/$target.json"
    
    print_info "Building tests for target: $target (library only)"
    
    # Build basic tests (library only, since linking requires Plan 9 tools)
    cd "$TESTS_DIR/basic"
    if cargo +nightly build --target "$target_file" -Zbuild-std=core,alloc --lib 2>/dev/null; then
        print_success "Basic tests built (library) for $target"
    else
        print_warning "Basic tests failed to build for $target (expected without Plan 9 linker)"
    fi
    
    # Build integration tests (library only)
    cd "$TESTS_DIR/integration"
    if cargo +nightly build --target "$target_file" -Zbuild-std=core,alloc --lib 2>/dev/null; then
        print_success "Integration tests built (library) for $target"
    else
        print_warning "Integration tests failed to build for $target (expected without Plan 9 linker)"
    fi
}

show_usage() {
    echo "Usage: $0 [OPTIONS] [TARGET]"
    echo
    echo "OPTIONS:"
    echo "  -h, --help     Show this help message"
    echo "  -r, --runtime  Build only runtime library"
    echo "  -t, --tests    Build only tests"
    echo "  -a, --all      Build for all targets (default)"
    echo
    echo "TARGETS:"
    for target in "${TARGETS[@]}"; do
        echo "  $target"
    done
    echo
    echo "If no target is specified, builds for all available targets."
}

main() {
    local build_runtime=true
    local build_tests=true
    local specific_target=""
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            -r|--runtime)
                build_tests=false
                shift
                ;;
            -t|--tests)
                build_runtime=false
                shift
                ;;
            -a|--all)
                # Default behavior
                shift
                ;;
            x86_64-unknown-plan9|i386-unknown-plan9)
                specific_target=$1
                shift
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    print_header
    check_prerequisites
    
    # Determine which targets to build
    local targets_to_build=()
    if [ -n "$specific_target" ]; then
        targets_to_build=("$specific_target")
    else
        targets_to_build=("${TARGETS[@]}")
    fi
    
    # Build for each target
    local success_count=0
    local total_count=${#targets_to_build[@]}
    
    for target in "${targets_to_build[@]}"; do
        echo
        print_info "Processing target: $target"
        echo "----------------------------------------"
        
        local target_success=true
        
        if [ "$build_runtime" = true ]; then
            if ! build_runtime "$target"; then
                target_success=false
            fi
        fi
        
        if [ "$build_tests" = true ]; then
            build_tests "$target"
        fi
        
        if [ "$target_success" = true ]; then
            ((success_count++))
        fi
    done
    
    # Summary
    echo
    echo "========================================"
    print_info "Build Summary"
    echo "========================================"
    print_info "Targets processed: $total_count"
    print_success "Successful builds: $success_count"
    
    if [ $success_count -eq $total_count ]; then
        print_success "All builds completed successfully!"
        echo
        print_info "Next steps:"
        echo "  1. Set up Plan 9 QEMU environment: cd tools/qemu-setup/plan9-env && ./start-plan9.sh"
        echo "  2. Install Plan 9 development tools (6l, 8l linkers)"
        echo "  3. Test binaries in actual Plan 9 system"
        echo "  4. Use tools/build-scripts/link-plan9.sh for linking in Plan 9"
    else
        print_warning "Some builds failed. Check the output above for details."
    fi
}

# Run main function with all arguments
main "$@"
