use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::str::FromStr;
use anyhow::Result;
use thiserror::Error;

/// Vietnamese tax calculation errors
#[derive(Error, Debug)]
pub enum TaxCalculationError {
    #[error("Invalid land area: {0}")]
    InvalidLandArea(String),
    #[error("Invalid land location: {0}")]
    InvalidLandLocation(String),
    #[error("Invalid land use type: {0}")]
    InvalidLandUseType(String),
    #[error("Invalid investment amount: {0}")]
    InvalidInvestmentAmount(String),
    #[error("Tax calculation failed: {0}")]
    CalculationFailed(String),
}

/// Land use types in Vietnam
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum LandUseType {
    Residential,
    Commercial,
    Industrial,
    Agricultural,
    Mixed,
}

/// Land location categories for tax calculation
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum LandLocationCategory {
    UrbanClass1, // Major cities like Ho Chi Minh City, Hanoi
    UrbanClass2, // Provincial cities
    UrbanClass3, // District towns
    Rural,       // Rural areas
}

/// Vietnamese tax calculation input
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaxCalculationInput {
    pub land_area: Decimal,           // in square meters
    pub land_value_per_sqm: Decimal,  // VND per square meter
    pub land_location: LandLocationCategory,
    pub land_use_type: LandUseType,
    pub total_investment: Decimal,    // Total project investment in VND
    pub construction_area: Option<Decimal>, // Built area in square meters
    pub project_duration_months: u32,
    pub is_foreign_investor: bool,
}

/// Vietnamese tax calculation results
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaxCalculationResult {
    pub land_use_fee: Decimal,
    pub registration_fee: Decimal,
    pub notarization_fee: Decimal,
    pub property_transfer_tax: Decimal,
    pub vat: Decimal,
    pub rental_income_tax: Option<RentalIncomeTax>,
    pub total_taxes_and_fees: Decimal,
    pub breakdown: TaxBreakdown,
}

/// Rental income tax calculation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RentalIncomeTax {
    pub annual_rental_income: Decimal,
    pub tax_free_threshold: Decimal,
    pub vat_rate: Decimal,
    pub personal_income_tax_rate: Decimal,
    pub business_license_tax: Decimal,
    pub total_rental_tax: Decimal,
}

/// Detailed tax breakdown
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaxBreakdown {
    pub land_use_fee_details: LandUseFeeDetails,
    pub registration_fee_details: RegistrationFeeDetails,
    pub notarization_fee_details: NotarizationFeeDetails,
    pub vat_details: VatDetails,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LandUseFeeDetails {
    pub base_rate: Decimal,
    pub location_multiplier: Decimal,
    pub use_type_multiplier: Decimal,
    pub calculated_fee: Decimal,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegistrationFeeDetails {
    pub base_fee: Decimal,
    pub area_based_fee: Decimal,
    pub total_fee: Decimal,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotarizationFeeDetails {
    pub asset_value: Decimal,
    pub fee_rate: Decimal,
    pub calculated_fee: Decimal,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VatDetails {
    pub taxable_amount: Decimal,
    pub vat_rate: Decimal,
    pub calculated_vat: Decimal,
}

/// Vietnamese tax calculator service
pub struct VietnameseTaxCalculator {
    tax_rates: TaxRateConfig,
}

/// Tax rate configuration based on Vietnamese law
#[derive(Debug, Clone)]
pub struct TaxRateConfig {
    pub land_use_fee_rates: HashMap<(LandLocationCategory, LandUseType), Decimal>,
    pub registration_fee_base: Decimal,
    pub notarization_fee_rates: Vec<(Decimal, Decimal)>, // (threshold, rate) pairs
    pub vat_rate: Decimal,
    pub property_transfer_tax_rate: Decimal,
    pub rental_income_tax_free_threshold: Decimal,
    pub rental_vat_rate: Decimal,
    pub rental_pit_rate: Decimal,
}

impl Default for TaxRateConfig {
    fn default() -> Self {
        let mut land_use_fee_rates = HashMap::new();
        
        // Land use fee rates based on Vietnamese regulations (VND per sqm per year)
        // Urban Class 1 (Major cities)
        land_use_fee_rates.insert((LandLocationCategory::UrbanClass1, LandUseType::Residential), Decimal::from(150000));
        land_use_fee_rates.insert((LandLocationCategory::UrbanClass1, LandUseType::Commercial), Decimal::from(300000));
        land_use_fee_rates.insert((LandLocationCategory::UrbanClass1, LandUseType::Industrial), Decimal::from(200000));
        land_use_fee_rates.insert((LandLocationCategory::UrbanClass1, LandUseType::Mixed), Decimal::from(250000));
        
        // Urban Class 2 (Provincial cities)
        land_use_fee_rates.insert((LandLocationCategory::UrbanClass2, LandUseType::Residential), Decimal::from(100000));
        land_use_fee_rates.insert((LandLocationCategory::UrbanClass2, LandUseType::Commercial), Decimal::from(200000));
        land_use_fee_rates.insert((LandLocationCategory::UrbanClass2, LandUseType::Industrial), Decimal::from(150000));
        land_use_fee_rates.insert((LandLocationCategory::UrbanClass2, LandUseType::Mixed), Decimal::from(175000));
        
        // Urban Class 3 (District towns)
        land_use_fee_rates.insert((LandLocationCategory::UrbanClass3, LandUseType::Residential), Decimal::from(50000));
        land_use_fee_rates.insert((LandLocationCategory::UrbanClass3, LandUseType::Commercial), Decimal::from(100000));
        land_use_fee_rates.insert((LandLocationCategory::UrbanClass3, LandUseType::Industrial), Decimal::from(75000));
        land_use_fee_rates.insert((LandLocationCategory::UrbanClass3, LandUseType::Mixed), Decimal::from(87500));
        
        // Rural areas
        land_use_fee_rates.insert((LandLocationCategory::Rural, LandUseType::Residential), Decimal::from(20000));
        land_use_fee_rates.insert((LandLocationCategory::Rural, LandUseType::Commercial), Decimal::from(40000));
        land_use_fee_rates.insert((LandLocationCategory::Rural, LandUseType::Industrial), Decimal::from(30000));
        land_use_fee_rates.insert((LandLocationCategory::Rural, LandUseType::Agricultural), Decimal::from(5000));
        land_use_fee_rates.insert((LandLocationCategory::Rural, LandUseType::Mixed), Decimal::from(35000));

        // Notarization fee rates based on asset value (threshold, rate)
        let notarization_fee_rates = vec![
            (Decimal::from(50_000_000), Decimal::from(50_000)),      // Below 50M VND
            (Decimal::from(100_000_000), Decimal::from(100_000)),    // 50M-100M VND
            (Decimal::from(1_000_000_000), "0.001".parse().unwrap()), // 100M-1B VND: 0.1%
            (Decimal::from(3_000_000_000i64), "0.0006".parse().unwrap()), // 1B-3B VND: 0.06%
            (Decimal::from(5_000_000_000i64), "0.0005".parse().unwrap()), // 3B-5B VND: 0.05%
            (Decimal::from(10_000_000_000i64), "0.0004".parse().unwrap()), // 5B-10B VND: 0.04%
            (Decimal::from(100_000_000_000i64), "0.0003".parse().unwrap()), // 10B-100B VND: 0.03%
            (Decimal::MAX, "0.0002".parse().unwrap()), // Above 100B VND: 0.02%
        ];

        Self {
            land_use_fee_rates,
            registration_fee_base: Decimal::from(500_000), // Base registration fee
            notarization_fee_rates,
            vat_rate: "0.05".parse().unwrap(), // 5% VAT
            property_transfer_tax_rate: "0.02".parse().unwrap(), // 2% for individuals
            rental_income_tax_free_threshold: Decimal::from(100_000_000), // 100M VND per year
            rental_vat_rate: "0.05".parse().unwrap(), // 5% VAT on rental
            rental_pit_rate: "0.05".parse().unwrap(), // 5% PIT on rental
        }
    }
}

impl VietnameseTaxCalculator {
    pub fn new() -> Self {
        Self {
            tax_rates: TaxRateConfig::default(),
        }
    }

    pub fn with_custom_rates(tax_rates: TaxRateConfig) -> Self {
        Self { tax_rates }
    }

    /// Calculate all applicable taxes and fees for a Vietnamese real estate project
    pub fn calculate_taxes(&self, input: &TaxCalculationInput) -> Result<TaxCalculationResult, TaxCalculationError> {
        // Validate input
        self.validate_input(input)?;

        // Calculate individual tax components
        let land_use_fee_details = self.calculate_land_use_fee(input)?;
        let registration_fee_details = self.calculate_registration_fee(input)?;
        let notarization_fee_details = self.calculate_notarization_fee(input)?;
        let vat_details = self.calculate_vat(input)?;
        let property_transfer_tax = self.calculate_property_transfer_tax(input)?;

        // Calculate rental income tax if applicable
        let rental_income_tax = if input.land_use_type == LandUseType::Commercial ||
                                   input.land_use_type == LandUseType::Mixed {
            Some(self.calculate_rental_income_tax(input)?)
        } else {
            None
        };

        let total_taxes_and_fees = land_use_fee_details.calculated_fee +
                                  registration_fee_details.total_fee +
                                  notarization_fee_details.calculated_fee +
                                  vat_details.calculated_vat +
                                  property_transfer_tax +
                                  rental_income_tax.as_ref().map(|r| r.total_rental_tax).unwrap_or(Decimal::ZERO);

        Ok(TaxCalculationResult {
            land_use_fee: land_use_fee_details.calculated_fee,
            registration_fee: registration_fee_details.total_fee,
            notarization_fee: notarization_fee_details.calculated_fee,
            property_transfer_tax,
            vat: vat_details.calculated_vat,
            rental_income_tax,
            total_taxes_and_fees,
            breakdown: TaxBreakdown {
                land_use_fee_details,
                registration_fee_details,
                notarization_fee_details,
                vat_details,
            },
        })
    }

    /// Calculate enhanced tax analysis with recommendations
    pub fn calculate_enhanced_tax_analysis(&self, input: &TaxCalculationInput) -> Result<EnhancedTaxAnalysis, TaxCalculationError> {
        let basic_calculation = self.calculate_taxes(input)?;

        // Calculate tax optimization opportunities
        let optimization_opportunities = self.identify_tax_optimizations(input);

        // Calculate compliance requirements
        let compliance_requirements = self.get_compliance_requirements(input);

        // Calculate payment schedule
        let payment_schedule = self.calculate_payment_schedule(&basic_calculation);

        Ok(EnhancedTaxAnalysis {
            basic_calculation,
            optimization_opportunities,
            compliance_requirements,
            payment_schedule,
            risk_assessment: self.assess_tax_risks(input),
        })
    }

    /// Identify tax optimization opportunities
    fn identify_tax_optimizations(&self, input: &TaxCalculationInput) -> Vec<TaxOptimization> {
        let mut optimizations = Vec::new();

        // Check for agricultural land exemptions
        if input.land_location == LandLocationCategory::Rural {
            optimizations.push(TaxOptimization {
                optimization_type: "Agricultural Exemption".to_string(),
                potential_savings: input.land_value_per_sqm * input.land_area * Decimal::from_str("0.5").unwrap(),
                description: "Consider agricultural land use classification for 50% tax reduction".to_string(),
                requirements: vec!["Land must be used for agricultural purposes".to_string()],
            });
        }

        // Check for phased development benefits
        if input.total_investment > Decimal::from(5_000_000_000i64) {
            optimizations.push(TaxOptimization {
                optimization_type: "Phased Development".to_string(),
                potential_savings: input.total_investment * Decimal::from_str("0.15").unwrap(),
                description: "Phased development can reduce upfront tax burden".to_string(),
                requirements: vec!["Develop project in phases over 3+ years".to_string()],
            });
        }

        optimizations
    }

    /// Get compliance requirements
    fn get_compliance_requirements(&self, input: &TaxCalculationInput) -> Vec<ComplianceRequirement> {
        let mut requirements = Vec::new();

        requirements.push(ComplianceRequirement {
            requirement_type: "Land Use Certificate".to_string(),
            deadline: "Before construction starts".to_string(),
            authority: "Department of Natural Resources and Environment".to_string(),
            estimated_cost: Decimal::from(5_000_000),
        });

        if input.total_investment > Decimal::from(15_000_000_000i64) {
            requirements.push(ComplianceRequirement {
                requirement_type: "Environmental Impact Assessment".to_string(),
                deadline: "6 months before construction".to_string(),
                authority: "Ministry of Natural Resources and Environment".to_string(),
                estimated_cost: Decimal::from(50_000_000),
            });
        }

        requirements
    }

    /// Calculate payment schedule
    fn calculate_payment_schedule(&self, calculation: &TaxCalculationResult) -> Vec<PaymentScheduleItem> {
        vec![
            PaymentScheduleItem {
                payment_type: "Land Use Fee".to_string(),
                amount: calculation.land_use_fee,
                due_date: "Upon land acquisition".to_string(),
                is_mandatory: true,
            },
            PaymentScheduleItem {
                payment_type: "Registration Fee".to_string(),
                amount: calculation.registration_fee,
                due_date: "Within 15 days of land transfer".to_string(),
                is_mandatory: true,
            },
            PaymentScheduleItem {
                payment_type: "VAT".to_string(),
                amount: calculation.vat,
                due_date: "Monthly declaration".to_string(),
                is_mandatory: true,
            },
        ]
    }

    /// Assess tax-related risks
    fn assess_tax_risks(&self, input: &TaxCalculationInput) -> TaxRiskAssessment {
        let mut risk_score = 0;
        let mut risk_factors = Vec::new();

        // High investment projects have higher scrutiny
        if input.total_investment > Decimal::from(10_000_000_000i64) {
            risk_score += 3;
            risk_factors.push("High-value project subject to increased tax authority scrutiny".to_string());
        }

        // Urban projects have more complex regulations
        if matches!(input.land_location, LandLocationCategory::UrbanClass1) {
            risk_score += 2;
            risk_factors.push("Urban Class 1 locations have complex zoning regulations".to_string());
        }

        let risk_level = match risk_score {
            0..=2 => "Low",
            3..=5 => "Medium",
            _ => "High",
        };

        TaxRiskAssessment {
            overall_risk_level: risk_level.to_string(),
            risk_score,
            risk_factors,
            mitigation_strategies: vec![
                "Engage qualified tax consultant".to_string(),
                "Maintain detailed documentation".to_string(),
                "Regular compliance reviews".to_string(),
            ],
        }
    }

    fn validate_input(&self, input: &TaxCalculationInput) -> Result<(), TaxCalculationError> {
        if input.land_area <= Decimal::ZERO {
            return Err(TaxCalculationError::InvalidLandArea("Land area must be positive".to_string()));
        }

        if input.land_value_per_sqm <= Decimal::ZERO {
            return Err(TaxCalculationError::InvalidLandArea("Land value per sqm must be positive".to_string()));
        }

        if input.total_investment <= Decimal::ZERO {
            return Err(TaxCalculationError::InvalidInvestmentAmount("Total investment must be positive".to_string()));
        }

        Ok(())
    }

    fn calculate_land_use_fee(&self, input: &TaxCalculationInput) -> Result<LandUseFeeDetails, TaxCalculationError> {
        let base_rate = self.tax_rates.land_use_fee_rates
            .get(&(input.land_location.clone(), input.land_use_type.clone()))
            .ok_or_else(|| TaxCalculationError::CalculationFailed(
                "No tax rate found for this land location and use type combination".to_string()
            ))?;

        let location_multiplier = match input.land_location {
            LandLocationCategory::UrbanClass1 => "1.5".parse().unwrap(),
            LandLocationCategory::UrbanClass2 => "1.2".parse().unwrap(),
            LandLocationCategory::UrbanClass3 => "1.0".parse().unwrap(),
            LandLocationCategory::Rural => "0.8".parse().unwrap(),
        };

        let use_type_multiplier = match input.land_use_type {
            LandUseType::Commercial => "1.3".parse().unwrap(),
            LandUseType::Industrial => "1.1".parse().unwrap(),
            LandUseType::Mixed => "1.2".parse().unwrap(),
            _ => Decimal::ONE,
        };

        let calculated_fee = base_rate * input.land_area * location_multiplier * use_type_multiplier;

        Ok(LandUseFeeDetails {
            base_rate: *base_rate,
            location_multiplier,
            use_type_multiplier,
            calculated_fee,
        })
    }

    fn calculate_registration_fee(&self, input: &TaxCalculationInput) -> Result<RegistrationFeeDetails, TaxCalculationError> {
        let base_fee = self.tax_rates.registration_fee_base;
        let area_based_fee = input.land_area * Decimal::from(1000); // 1000 VND per sqm
        let total_fee = base_fee + area_based_fee;

        Ok(RegistrationFeeDetails {
            base_fee,
            area_based_fee,
            total_fee,
        })
    }

    fn calculate_notarization_fee(&self, input: &TaxCalculationInput) -> Result<NotarizationFeeDetails, TaxCalculationError> {
        let asset_value = input.land_area * input.land_value_per_sqm;
        
        let (fee_rate, calculated_fee) = self.calculate_progressive_fee(&asset_value, &self.tax_rates.notarization_fee_rates);

        Ok(NotarizationFeeDetails {
            asset_value,
            fee_rate,
            calculated_fee,
        })
    }

    fn calculate_vat(&self, input: &TaxCalculationInput) -> Result<VatDetails, TaxCalculationError> {
        let taxable_amount = input.total_investment;
        let vat_rate = self.tax_rates.vat_rate;
        let calculated_vat = taxable_amount * vat_rate;

        Ok(VatDetails {
            taxable_amount,
            vat_rate,
            calculated_vat,
        })
    }

    fn calculate_property_transfer_tax(&self, input: &TaxCalculationInput) -> Result<Decimal, TaxCalculationError> {
        let transfer_value = input.land_area * input.land_value_per_sqm;
        let tax_rate = if input.is_foreign_investor {
            self.tax_rates.property_transfer_tax_rate * "1.5".parse::<Decimal>().unwrap() // Higher rate for foreign investors
        } else {
            self.tax_rates.property_transfer_tax_rate
        };
        
        Ok(transfer_value * tax_rate)
    }

    fn calculate_rental_income_tax(&self, input: &TaxCalculationInput) -> Result<RentalIncomeTax, TaxCalculationError> {
        // Estimate annual rental income based on investment and location
        let estimated_rental_yield: Decimal = match input.land_location {
            LandLocationCategory::UrbanClass1 => "0.06".parse().unwrap(), // 6% yield
            LandLocationCategory::UrbanClass2 => "0.08".parse().unwrap(), // 8% yield
            LandLocationCategory::UrbanClass3 => "0.10".parse().unwrap(), // 10% yield
            LandLocationCategory::Rural => "0.12".parse().unwrap(), // 12% yield
        };

        let annual_rental_income = input.total_investment * estimated_rental_yield;
        let tax_free_threshold = self.tax_rates.rental_income_tax_free_threshold;

        let taxable_income = if annual_rental_income > tax_free_threshold {
            annual_rental_income - tax_free_threshold
        } else {
            Decimal::ZERO
        };

        let vat = if annual_rental_income > tax_free_threshold {
            annual_rental_income * self.tax_rates.rental_vat_rate
        } else {
            Decimal::ZERO
        };

        let personal_income_tax = taxable_income * self.tax_rates.rental_pit_rate;

        let business_license_tax = if annual_rental_income > tax_free_threshold {
            match annual_rental_income {
                income if income <= Decimal::from(300_000_000) => Decimal::from(300_000),
                income if income <= Decimal::from(500_000_000) => Decimal::from(500_000),
                _ => Decimal::from(1_000_000),
            }
        } else {
            Decimal::ZERO
        };

        let total_rental_tax = vat + personal_income_tax + business_license_tax;

        Ok(RentalIncomeTax {
            annual_rental_income,
            tax_free_threshold,
            vat_rate: self.tax_rates.rental_vat_rate,
            personal_income_tax_rate: self.tax_rates.rental_pit_rate,
            business_license_tax,
            total_rental_tax,
        })
    }

    fn calculate_progressive_fee(&self, amount: &Decimal, rate_table: &[(Decimal, Decimal)]) -> (Decimal, Decimal) {
        for (threshold, rate) in rate_table {
            if amount <= threshold {
                if *rate < Decimal::ONE {
                    // Percentage rate
                    return (*rate, amount * rate);
                } else {
                    // Fixed amount
                    return (*rate, *rate);
                }
            }
        }
        
        // Fallback to last rate
        let last_rate = rate_table.last().unwrap().1;
        (last_rate, amount * last_rate)
    }
}

impl Default for VietnameseTaxCalculator {
    fn default() -> Self {
        Self::new()
    }
}

// Helper functions for parsing location and use type from strings
impl LandLocationCategory {
    pub fn from_string(location: &str) -> Result<Self, TaxCalculationError> {
        match location.to_lowercase().as_str() {
            "urban_class_1" | "major_city" | "ho_chi_minh" | "hanoi" => Ok(LandLocationCategory::UrbanClass1),
            "urban_class_2" | "provincial_city" => Ok(LandLocationCategory::UrbanClass2),
            "urban_class_3" | "district_town" => Ok(LandLocationCategory::UrbanClass3),
            "rural" | "countryside" => Ok(LandLocationCategory::Rural),
            _ => Err(TaxCalculationError::InvalidLandLocation(format!("Unknown location: {}", location))),
        }
    }
}

impl LandUseType {
    pub fn from_string(use_type: &str) -> Result<Self, TaxCalculationError> {
        match use_type.to_lowercase().as_str() {
            "residential" | "housing" => Ok(LandUseType::Residential),
            "commercial" | "business" => Ok(LandUseType::Commercial),
            "industrial" | "factory" => Ok(LandUseType::Industrial),
            "agricultural" | "farming" => Ok(LandUseType::Agricultural),
            "mixed" | "mixed_use" => Ok(LandUseType::Mixed),
            _ => Err(TaxCalculationError::InvalidLandUseType(format!("Unknown use type: {}", use_type))),
        }
    }
}
