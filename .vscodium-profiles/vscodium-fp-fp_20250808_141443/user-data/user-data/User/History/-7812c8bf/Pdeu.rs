use super::base::BaseRepository;
use super::{Repository, RepositoryError};
use crate::entities::audit_logs;
use anyhow::Result;
use async_trait::async_trait;
use sea_orm::{ColumnTrait, DatabaseConnection, DbErr, QueryFilter, QueryOrder, Order, EntityTrait, Condition};
use uuid::Uuid;
use chrono::{DateTime, Utc};

/// Audit repository for managing audit log entities
pub struct AuditRepository {
    base: BaseRepository<audit_logs::Entity, audit_logs::Model, audit_logs::ActiveModel>,
}

impl AuditRepository {
    pub fn new(db: DatabaseConnection) -> Self {
        Self {
            base: BaseRepository::new(db),
        }
    }

    /// Find audit logs by user ID
    pub async fn find_by_user(&self, user_id: Uuid) -> Result<Vec<audit_logs::Model>, DbErr> {
        self.base
            .find_by_condition(audit_logs::Column::UserId.eq(user_id))
            .await
    }

    /// Find audit logs by entity
    pub async fn find_by_entity(&self, entity_type: &str, entity_id: Uuid) -> Result<Vec<audit_logs::Model>, DbErr> {
        audit_logs::Entity::find()
            .filter(audit_logs::Column::EntityType.eq(entity_type))
            .filter(audit_logs::Column::EntityId.eq(entity_id))
            .order_by_desc(audit_logs::Column::CreatedAt)
            .all(self.base.db())
            .await
    }

    /// Find audit logs by action
    pub async fn find_by_action(&self, action: &str) -> Result<Vec<audit_logs::Model>, DbErr> {
        self.base
            .find_by_condition(audit_logs::Column::Action.eq(action))
            .await
    }

    /// Find audit logs within date range
    pub async fn find_by_date_range(
        &self,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
    ) -> Result<Vec<audit_logs::Model>, DbErr> {
        audit_logs::Entity::find()
            .filter(audit_logs::Column::CreatedAt.gte(start_date))
            .filter(audit_logs::Column::CreatedAt.lte(end_date))
            .order_by_desc(audit_logs::Column::CreatedAt)
            .all(self.base.db())
            .await
    }

    /// Get recent audit logs
    pub async fn find_recent(&self, limit: u64) -> Result<Vec<audit_logs::Model>, DbErr> {
        audit_logs::Entity::find()
            .order_by_desc(audit_logs::Column::CreatedAt)
            .limit(limit)
            .all(self.base.db())
            .await
    }

    /// Clean up old audit logs (older than specified days)
    pub async fn cleanup_old_logs(&self, days: i64) -> Result<u64, DbErr> {
        let cutoff_date = Utc::now() - chrono::Duration::days(days);
        self.base
            .delete_by_condition(audit_logs::Column::CreatedAt.lt(cutoff_date))
            .await
    }
}

#[async_trait]
impl Repository<audit_logs::Model, Uuid> for AuditRepository {
    async fn find_by_id(&self, id: Uuid) -> Result<Option<audit_logs::Model>, DbErr> {
        self.base.find_by_pk(id).await
    }

    async fn find_all(&self, limit: Option<u64>, offset: Option<u64>) -> Result<Vec<audit_logs::Model>, DbErr> {
        match (limit, offset) {
            (Some(l), Some(o)) => self.base.find_with_limit(l, o).await,
            _ => self.base.find_all().await,
        }
    }

    async fn create(&self, entity: audit_logs::Model) -> Result<audit_logs::Model, DbErr> {
        let active_model: audit_logs::ActiveModel = entity.into();
        self.base.create(active_model).await
    }

    async fn update(&self, entity: audit_logs::Model) -> Result<audit_logs::Model, DbErr> {
        let active_model: audit_logs::ActiveModel = entity.into();
        self.base.update(active_model).await
    }

    async fn delete(&self, id: Uuid) -> Result<bool, DbErr> {
        self.base.delete_by_pk(id).await
    }

    async fn count(&self) -> Result<u64, DbErr> {
        self.base.count().await
    }

    async fn exists(&self, id: Uuid) -> Result<bool, DbErr> {
        self.base.exists_by_pk(id).await
    }
}
