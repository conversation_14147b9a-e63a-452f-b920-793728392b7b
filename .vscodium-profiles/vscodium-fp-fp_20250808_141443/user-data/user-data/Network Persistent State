{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": [], "broken_count": 1, "host": "augment-assets.com", "port": 443, "protocol_str": "quic"}, {"anonymization": [], "broken_count": 1, "host": "unpkg.com", "port": 443, "protocol_str": "quic"}, {"anonymization": [], "broken_count": 1, "host": "fonts.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": [], "broken_count": 5, "broken_until": "1754641143", "host": "cdnjs.cloudflare.com", "port": 443, "protocol_str": "quic"}], "servers": [{"anonymization": [], "server": "https://augment-assets.com", "supports_spdy": true}, {"anonymization": [], "server": "https://raw.githubusercontent.com", "supports_spdy": true}, {"anonymization": [], "server": "https://unpkg.com", "supports_spdy": true}, {"anonymization": [], "server": "https://open-vsx.org", "supports_spdy": true}, {"anonymization": [], "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401702920618835", "port": 443, "protocol_str": "quic"}], "anonymization": [], "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399198740085089", "port": 443, "protocol_str": "quic"}], "anonymization": [], "server": "https://cdnjs.cloudflare.com", "supports_spdy": true}], "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "4G", "CAISABiAgICA+P////8B": "4G", "CAYSABiAgICA+P////8B": "Offline"}}}