#!/bin/bash
set -e

log_info() { echo -e "\033[0;34mℹ️  $1\033[0m"; }

log_info "Cleaning up macOS sandbox..."

# Kill any remaining VSCodium processes for this profile
pkill -f "vscodium-fp-fp_20250808_150139" 2>/dev/null || true

# Clean up temporary directories
if [[ -n "${TMPDIR:-}" ]] && [[ "$TMPDIR" =~ /tmp/vscodium- ]]; then
    rm -rf "$TMPDIR" 2>/dev/null || true
fi

# Clean up any remaining temporary files
find /tmp -name "*vscodium*" -user "$(whoami)" -mtime +1 -delete 2>/dev/null || true

log_info "macOS sandbox cleanup completed"
