{"id": "27381f58-d3d3-4fa4-9e3e-f0fed07d6680", "version": "1.0.0", "created_at": "2025-08-08T08:01:41.341803Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-09T08:01:41.341803Z", "device_id": "j4f8LI_fQgw8M_ZUYfNEcw", "hardware_signature": {"cpu_signature": "apple-apple-silicon-27", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "22So8uPAGOQPtaPIYp-Lqw", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "OrTd7t9Xat_brngdFHy5Tw", "username_hash": "Qy_EI3h6-WUrK-uWvlCPYg"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "dh8gTqnOsvWcNcQMZQENuA", "session_signature": "UMeztODz8r8", "workspace_signature": "Pe0VJimGMik", "extensions_signature": "9vdLd1FkUzA"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-Jytc-dtn", "network_class": "ethernet", "connectivity_signature": "Hs_NyCIG5AQ"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "e5f10f801fcb3c8a1cbc2d1e90945c01203de1fd268057863a71789054e98f1e"}