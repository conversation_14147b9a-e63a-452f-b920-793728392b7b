{"id": "f4ecd85d-84f9-4b20-aeb0-acb15e9dcefa", "version": "1.0.0", "created_at": "2025-08-08T08:07:27.461807Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-09T08:07:27.461807Z", "device_id": "OH3KF2aH8X5ZwkQOeL1qYQ", "hardware_signature": {"cpu_signature": "apple-apple-silicon-784", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "GOMePjQrARQDQ7JvqaAOPA", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "sL4wMaWQN_DRWjSL2XtOqQ", "username_hash": "0P7PWzlc0kB1DRMvF1UbyQ"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "UEKF7CmpAMZHwx0HeU5wUQ", "session_signature": "0IUoIJJ-3BE", "workspace_signature": "cbaUNE1XXZ4", "extensions_signature": "AHul_W29HhY"}, "network_signature": {"interface_count": 1, "mac_signature": "generic--S-MtNJ4", "network_class": "ethernet", "connectivity_signature": "rKfMbGccM8A"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "fdfb83f5800b0f9c0544619792ab68d75e4e4c3e111c6dfdb44b77c3b601a847"}