{"id": "5ecc9656-400d-497f-8b58-e3ac68769f4a", "version": "1.0.0", "created_at": "2025-08-08T08:02:13.579167Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-09T08:02:13.579167Z", "device_id": "5ERneduiXi5UAziojBvrhw", "hardware_signature": {"cpu_signature": "apple-apple-silicon-775", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "d_Ty6sI03Yg5jxh2dTQFdw", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "vTOz9q6cVqmNWBkzKazw-w", "username_hash": "BgDnY3TJo4uF1sd2F8D2VQ"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "C1qaDYtd_5fSMZPgZL_V4w", "session_signature": "0yDBq6w3OK4", "workspace_signature": "fJ0yGsnfB8k", "extensions_signature": "FErbpOTVoPQ"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-4pGfl10q", "network_class": "ethernet", "connectivity_signature": "R65xSiaVCfE"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "cbd54a07a75961482fc660463a6617ff189c0b6df50d1f4662861341a4d0f3d4"}