#!/bin/bash
# Enhanced Sandboxed VSCodium Launcher
# Provides comprehensive isolation even without advanced sandbox technologies

set -euo pipefail

# Logging functions
log_info() { echo -e "\033[0;34mℹ️  $1\033[0m" >&2; }
log_warning() { echo -e "\033[1;33m⚠️  $1\033[0m" >&2; }
log_error() { echo -e "\033[0;31m❌ $1\033[0m" >&2; }

log_info "Starting enhanced basic sandbox for VSCodium"

# Create isolated temporary directory
export TMPDIR="/tmp/vscodium-isolated-$$"
mkdir -p "$TMPDIR"
chmod 700 "$TMPDIR"

# Set comprehensive environment variables for isolation
export VSCODE_PORTABLE="/Users/<USER>/Documents/augment-projects/exploit-extension/.vscodium-profiles/vscodium-fp-fp_20250808_150551/user-data"
export VSCODE_APPDATA="/Users/<USER>/Documents/augment-projects/exploit-extension/.vscodium-profiles/vscodium-fp-fp_20250808_150551/user-data"
export VSCODE_LOGS="/Users/<USER>/Documents/augment-projects/exploit-extension/.vscodium-profiles/vscodium-fp-fp_20250808_150551/user-data/logs"
export XDG_CONFIG_HOME="/Users/<USER>/Documents/augment-projects/exploit-extension/.vscodium-profiles/vscodium-fp-fp_20250808_150551/user-data"
export XDG_DATA_HOME="/Users/<USER>/Documents/augment-projects/exploit-extension/.vscodium-profiles/vscodium-fp-fp_20250808_150551/user-data/data"
export XDG_CACHE_HOME="/Users/<USER>/Documents/augment-projects/exploit-extension/.vscodium-profiles/vscodium-fp-fp_20250808_150551/user-data/cache"
export XDG_RUNTIME_DIR="$TMPDIR"

# Create required directories
mkdir -p "$XDG_DATA_HOME" "$XDG_CACHE_HOME" "$VSCODE_LOGS"

# Apply comprehensive resource limits if enabled
if [[ "false" == "true" ]]; then
    log_info "Applying resource limits"

    # Set comprehensive ulimits (macOS compatible)
    ulimit -c 0          # No core dumps
    ulimit -f 1048576    # Max file size 1GB
    ulimit -n 1024       # Max open files
    ulimit -u 512        # Max processes
    ulimit -s 8192       # Max stack size 8MB
    ulimit -t 3600       # Max CPU time 1 hour

    # Virtual memory limit (macOS compatible)
    if [[ "$(uname)" == "Darwin" ]]; then
        ulimit -d 2097152 2>/dev/null || true  # Max data segment 2GB (macOS)
    else
        ulimit -v 2097152 2>/dev/null || true  # Max virtual memory 2GB (Linux)
    fi

    # Set process priority (lower priority)
    renice 10 $$ 2>/dev/null || true

    # Set I/O priority (lower priority)
    ionice -c 3 -p $$ 2>/dev/null || true
fi

# Set restrictive umask for created files
umask 077

# Clear potentially sensitive environment variables
unset SSH_AUTH_SOCK SSH_AGENT_PID GPG_AGENT_INFO
unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
unset GOOGLE_APPLICATION_CREDENTIALS
unset AZURE_CLIENT_ID AZURE_CLIENT_SECRET AZURE_TENANT_ID

# Set secure PATH (remove potentially dangerous directories)
export PATH="/usr/local/bin:/usr/bin:/bin"

# Apply network restrictions if possible
case "restrict" in
    block)
        log_info "Network access blocked (basic isolation)"
        # Set environment to block network usage
        export http_proxy="http://127.0.0.1:1"
        export https_proxy="http://127.0.0.1:1"
        export ftp_proxy="http://127.0.0.1:1"
        export no_proxy=""
        ;;
    restrict)
        log_info "Network access restricted to localhost"
        # Set environment to discourage external network usage
        export http_proxy="http://127.0.0.1:8080"
        export https_proxy="http://127.0.0.1:8080"
        export ftp_proxy="http://127.0.0.1:8080"
        export no_proxy="localhost,127.0.0.1"
        ;;
    allow)
        log_info "Network access allowed"
        ;;
esac

# Create process monitoring script
cat > "$TMPDIR/monitor.sh" << 'MONITOR_EOF'
#!/bin/bash
# Process monitor for sandboxed VSCodium

PARENT_PID=$1
PROFILE_NAME="vscodium-fp-fp_20250808_150551"
LOG_FILE="/Users/<USER>/Documents/augment-projects/exploit-extension/.vscodium-profiles/vscodium-fp-fp_20250808_150551/user-data/sandbox-monitor.log"

log_monitor() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

log_monitor "Starting sandbox monitor for PID $PARENT_PID"

# Monitor resource usage
while kill -0 "$PARENT_PID" 2>/dev/null; do
    # Get process info
    if ps -p "$PARENT_PID" -o pid,ppid,pcpu,pmem,vsz,rss,comm > /dev/null 2>&1; then
        PROC_INFO=$(ps -p "$PARENT_PID" -o pcpu,pmem,vsz,rss --no-headers 2>/dev/null || echo "N/A")
        log_monitor "Resource usage: $PROC_INFO"

        # Check for suspicious activity (with error handling)
        CPU_USAGE=$(echo "$PROC_INFO" | awk '{print $1}' | cut -d. -f1 2>/dev/null || echo "0")
        MEM_USAGE=$(echo "$PROC_INFO" | awk '{print $2}' | cut -d. -f1 2>/dev/null || echo "0")

        if [[ "$CPU_USAGE" =~ ^[0-9]+$ ]] && [[ "$CPU_USAGE" -gt 80 ]]; then
            log_monitor "WARNING: High CPU usage: $CPU_USAGE%"
        fi

        if [[ "$MEM_USAGE" =~ ^[0-9]+$ ]] && [[ "$MEM_USAGE" -gt 50 ]]; then
            log_monitor "WARNING: High memory usage: $MEM_USAGE%"
        fi
    fi

    sleep 30
done

log_monitor "Sandbox monitor finished for PID $PARENT_PID"
MONITOR_EOF

chmod +x "$TMPDIR/monitor.sh"

# Cleanup function
cleanup() {
    log_info "Cleaning up enhanced basic sandbox"

    # Kill any remaining child processes
    pkill -P $$ 2>/dev/null || true

    # Clean up temporary directory
    rm -rf "$TMPDIR" 2>/dev/null || true

    log_info "Enhanced basic sandbox cleanup completed"
}

# Set up signal handlers
trap cleanup EXIT INT TERM

# Start process monitor in background (only if resource limits enabled)
if [[ "false" == "true" ]]; then
    "$TMPDIR/monitor.sh" $$ &
    MONITOR_PID=$!
fi

# Set process name to distinguish from system VSCodium
exec -a "🔒 Enhanced Sandboxed VSCodium (vscodium-fp-fp_20250808_150551)" "$@"
