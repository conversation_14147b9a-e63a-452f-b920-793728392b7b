{"id": "c5e3bb28-d485-4825-9974-0d41ffbb7ccc", "version": "1.0.0", "created_at": "2025-08-08T08:05:52.241187Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-09T08:05:52.241187Z", "device_id": "45hOlF--oMFtJKD6dy0vYw", "hardware_signature": {"cpu_signature": "apple-apple-silicon-877", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "98KlYhKsuNrWmkZ3z40KGQ", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "hiAZPtdvJZ9ZSdKynEDi3Q", "username_hash": "_KSjTtMh4qRy3Ge1YMk5LQ"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "r35o4_YAxOnTdP9Vdm3omg", "session_signature": "iYOa3of9x0o", "workspace_signature": "skG1vlP6wtw", "extensions_signature": "1_ELwSXGzdE"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-wePy9zE2", "network_class": "ethernet", "connectivity_signature": "PA3daFkXVWc"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "2904d7002b987c5eab44d1ae30eac6d564edb84082f42f8e421388168cd9923a"}