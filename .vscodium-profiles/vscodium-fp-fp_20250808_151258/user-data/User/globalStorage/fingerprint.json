{"id": "eb895884-9336-4116-a2ae-badcbc8f0c4b", "version": "1.0.0", "created_at": "2025-08-08T08:12:59.735054Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-09T08:12:59.735054Z", "device_id": "WWfnxg_UocbBcNH0N9SZ7w", "hardware_signature": {"cpu_signature": "apple-apple-silicon-599", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "JSr4TcEPxJ3ouNCUdf65UQ", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "J96Oe9QleSCC8FOLBTfDag", "username_hash": "9JucI2ED8DiOry3HOiMRng"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "AweHW4uZ29czfRwJUznODA", "session_signature": "CT-BGcfGbx0", "workspace_signature": "xJTOvWJSmEc", "extensions_signature": "UKnlEJWskz8"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-NN71wxFn", "network_class": "ethernet", "connectivity_signature": "ocB-8-kgOho"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "ca1842ec1c931ea271dd20c77b94b1170f9026bdba578c20d43344c426b176b2"}