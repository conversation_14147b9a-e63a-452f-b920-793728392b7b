{"id": "0a130c31-15d2-4127-9e21-add93994b3fb", "version": "1.0.0", "created_at": "2025-08-08T08:12:23.107188Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-09T08:12:23.107188Z", "device_id": "6K3Yt3JTW80sEP6tnR65gQ", "hardware_signature": {"cpu_signature": "apple-apple-silicon-558", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "G7V4WiWzscCUhrPATatG_g", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "a5Ft6s-YwUHG5n_DGYwuuw", "username_hash": "XXdbcoPVO1N_jnhDxuXi2w"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "JDLLzyfC867C4h31cX2slQ", "session_signature": "sWSt1nz5Tfc", "workspace_signature": "mMJt3ob6S2Q", "extensions_signature": "bGsnyJHMWOU"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-AZHNHjMx", "network_class": "ethernet", "connectivity_signature": "KAkcFa_MFco"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "7dbd75a10a903981feb2195dad6fc7349f6a55485e8089b053678e365d751672"}