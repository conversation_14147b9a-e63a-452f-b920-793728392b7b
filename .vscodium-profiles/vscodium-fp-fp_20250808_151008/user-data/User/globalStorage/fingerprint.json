{"id": "7173d893-340e-4683-afe9-2af1708b2d89", "version": "1.0.0", "created_at": "2025-08-08T08:10:09.888289Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-09T08:10:09.888290Z", "device_id": "BvInMgIvfjQZzeesQ7gDgA", "hardware_signature": {"cpu_signature": "apple-apple-silicon-632", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "0BZ4-Gjyfu2yX0mWtvxcKQ", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "2nzSWGx7mQbFXYY2QbN6CA", "username_hash": "YyRbAKqDovq8AJopxss79g"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "RB4UjX8LuUQSDWjj9eigjQ", "session_signature": "jMxWIxpGI80", "workspace_signature": "4rUYy9GLNQg", "extensions_signature": "5m4YuV-hQgE"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-1PDmgrVZ", "network_class": "ethernet", "connectivity_signature": "0NFQGGtBy-Q"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "70c5ac5ea77cdf9ae76b9bcc090e1ee567d3cee492414261d0dd9d128c3e8707"}